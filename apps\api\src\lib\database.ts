import { PrismaClient } from '@prisma/client';

// 全局声明，避免开发环境下重复创建实例
declare global {
  var __prisma: PrismaClient | undefined;
}

// 创建Prisma客户端实例
export const prisma = globalThis.__prisma || new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
});

// 开发环境下保存实例到全局变量，避免热重载时重复创建
if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma;
}

// 优雅关闭数据库连接
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});

process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

// 数据库健康检查
export const checkDatabaseConnection = async (): Promise<boolean> => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    return false;
  }
};

// 初始化数据库
export const initializeDatabase = async (): Promise<void> => {
  try {
    // 现在不再创建默认用户，因为我们有了完整的用户注册系统
    // 用户需要通过注册API创建账户
    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
};

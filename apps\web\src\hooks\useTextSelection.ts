'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

interface TextSelection {
  text: string;
  position: { x: number; y: number };
  range: Range | null;
}

interface UseTextSelectionOptions {
  minLength?: number;
  maxLength?: number;
  containerRef?: React.RefObject<HTMLElement>;
  onSelectionChange?: (selection: TextSelection | null) => void;
}

interface UseTextSelectionReturn {
  selection: TextSelection | null;
  clearSelection: () => void;
  isSelecting: boolean;
}

export const useTextSelection = ({
  minLength = 1,
  maxLength = 500,
  containerRef,
  onSelectionChange
}: UseTextSelectionOptions = {}): UseTextSelectionReturn => {
  const [selection, setSelection] = useState<TextSelection | null>(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const selectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 获取选中文本的位置
  const getSelectionPosition = useCallback((range: Range): { x: number; y: number } => {
    const rect = range.getBoundingClientRect();
    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
    const scrollY = window.pageYOffset || document.documentElement.scrollTop;
    
    return {
      x: rect.left + scrollX + rect.width / 2,
      y: rect.top + scrollY
    };
  }, []);

  // 处理文本选择
  const handleSelectionChange = useCallback(() => {
    const windowSelection = window.getSelection();
    
    if (!windowSelection || windowSelection.rangeCount === 0) {
      setSelection(null);
      setIsSelecting(false);
      return;
    }

    const range = windowSelection.getRangeAt(0);
    const selectedText = windowSelection.toString().trim();

    // 检查选中文本长度
    if (selectedText.length < minLength || selectedText.length > maxLength) {
      setSelection(null);
      setIsSelecting(false);
      return;
    }

    // 检查是否在指定容器内
    if (containerRef?.current) {
      const container = containerRef.current;
      const commonAncestor = range.commonAncestorContainer;
      
      // 检查选中的内容是否在容器内
      if (!container.contains(commonAncestor)) {
        setSelection(null);
        setIsSelecting(false);
        return;
      }
    }

    // 获取选中文本的位置
    const position = getSelectionPosition(range);

    const newSelection: TextSelection = {
      text: selectedText,
      position,
      range: range.cloneRange()
    };

    setSelection(newSelection);
    setIsSelecting(false);

    // 调用回调函数
    if (onSelectionChange) {
      onSelectionChange(newSelection);
    }
  }, [minLength, maxLength, containerRef, getSelectionPosition, onSelectionChange]);

  // 清除选择
  const clearSelection = useCallback(() => {
    const windowSelection = window.getSelection();
    if (windowSelection) {
      windowSelection.removeAllRanges();
    }
    setSelection(null);
    setIsSelecting(false);
    
    if (onSelectionChange) {
      onSelectionChange(null);
    }
  }, [onSelectionChange]);

  // 处理鼠标按下事件
  const handleMouseDown = useCallback(() => {
    setIsSelecting(true);
    setSelection(null);
    
    // 清除之前的定时器
    if (selectionTimeoutRef.current) {
      clearTimeout(selectionTimeoutRef.current);
    }
  }, []);

  // 处理鼠标抬起事件
  const handleMouseUp = useCallback(() => {
    // 延迟处理选择，确保选择操作完成
    selectionTimeoutRef.current = setTimeout(() => {
      handleSelectionChange();
    }, 10);
  }, [handleSelectionChange]);

  // 处理键盘事件（Escape键清除选择）
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape' && selection) {
      clearSelection();
    }
  }, [selection, clearSelection]);

  // 设置事件监听器
  useEffect(() => {
    const targetElement = containerRef?.current || document;

    // 添加事件监听器
    targetElement.addEventListener('mousedown', handleMouseDown);
    targetElement.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('selectionchange', handleSelectionChange);
    document.addEventListener('keydown', handleKeyDown);

    // 清理函数
    return () => {
      targetElement.removeEventListener('mousedown', handleMouseDown);
      targetElement.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('selectionchange', handleSelectionChange);
      document.removeEventListener('keydown', handleKeyDown);
      
      // 清除定时器
      if (selectionTimeoutRef.current) {
        clearTimeout(selectionTimeoutRef.current);
      }
    };
  }, [containerRef, handleMouseDown, handleMouseUp, handleSelectionChange, handleKeyDown]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (selectionTimeoutRef.current) {
        clearTimeout(selectionTimeoutRef.current);
      }
    };
  }, []);

  return {
    selection,
    clearSelection,
    isSelecting
  };
};

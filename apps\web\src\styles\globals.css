@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS变量定义 */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    
    /* 字体变量 */
    --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
    --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
    --font-reading: Georgia, "Times New Roman", "Noto Serif SC", "Source Han Serif SC", serif;
    
    /* 阅读主题变量 */
    --reading-text: #1a1a1a;
    --reading-bg: #ffffff;
    --reading-secondary: #f5f5f5;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    
    /* 暗色主题阅读变量 */
    --reading-text: #e5e5e5;
    --reading-bg: #1a1a1a;
    --reading-secondary: #2a2a2a;
  }
  
  /* 护眼主题 */
  .sepia {
    --reading-text: #5c4b37;
    --reading-bg: #f4f1ea;
    --reading-secondary: #ede4d3;
  }
}

/* 基础样式重置 */
@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-secondary;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-md;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
  
  /* 选择文本样式 */
  ::selection {
    @apply bg-primary/20;
  }
  
  /* 焦点样式 */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }
}

/* 组件样式 */
@layer components {
  /* 阅读容器 */
  .reading-container {
    background-color: var(--reading-bg);
    color: var(--reading-text);
    transition: background-color 0.3s ease, color 0.3s ease;
  }
  
  /* 阅读文本 */
  .reading-text {
    font-family: var(--font-reading);
    line-height: 1.8;
    letter-spacing: 0.02em;
  }
  
  /* 段落样式 */
  .reading-paragraph {
    @apply mb-6;
    text-indent: 2em;
    text-align: justify;
  }
  
  /* 章节标题 */
  .chapter-title {
    @apply text-2xl font-bold mb-8 text-center;
    font-family: var(--font-reading);
  }
  
  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-primary border-t-transparent;
  }
  
  /* 卡片悬停效果 */
  .card-hover {
    @apply transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
  }
  
  /* 按钮动画 */
  .button-press {
    @apply transition-transform duration-75 active:scale-95;
  }
}

/* 工具类 */
@layer utilities {
  /* 文本省略 */
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* 隐藏滚动条 */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  /* 渐变遮罩 */
  .gradient-mask-b {
    mask-image: linear-gradient(to bottom, black 0%, black 90%, transparent 100%);
  }
  
  .gradient-mask-r {
    mask-image: linear-gradient(to right, black 0%, black 90%, transparent 100%);
  }
}

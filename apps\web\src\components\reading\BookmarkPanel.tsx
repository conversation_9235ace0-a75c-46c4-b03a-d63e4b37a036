"use client";

import { useState, useEffect } from 'react';
import { api } from '@/lib/api';

export interface Bookmark {
  id: string;
  position: number;
  note?: string;
  type: string;
  createdAt: string;
  chapter: {
    title: string;
    orderIndex: number;
  };
}

interface BookmarkPanelProps {
  isOpen: boolean;
  onClose: () => void;
  bookId: string;
  currentChapterIndex: number;
  currentChapterId?: string;
  onJumpToBookmark: (chapterIndex: number, position: number) => void;
}

export default function BookmarkPanel({ 
  isOpen, 
  onClose, 
  bookId, 
  currentChapterIndex,
  currentChapterId,
  onJumpToBookmark 
}: BookmarkPanelProps) {
  const [bookmarks, setBookmarks] = useState<Bookmark[]>([]);
  const [loading, setLoading] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newNote, setNewNote] = useState('');

  useEffect(() => {
    if (isOpen) {
      loadBookmarks();
    }
  }, [isOpen, bookId]);

  const loadBookmarks = async () => {
    try {
      setLoading(true);
      const response = await api.bookmarks.list(bookId);
      if (response.success) {
        setBookmarks(response.data || []);
      }
    } catch (error) {
      console.error('Failed to load bookmarks:', error);
    } finally {
      setLoading(false);
    }
  };

  const addBookmark = async () => {
    if (!currentChapterId) return;
    
    try {
      const response = await api.bookmarks.create({
        bookId,
        chapterId: currentChapterId,
        position: 0, // 简化实现，使用章节开头
        note: newNote.trim() || `第${currentChapterIndex + 1}章书签`
      });
      
      if (response.success) {
        setBookmarks(prev => [response.data, ...prev]);
        setNewNote('');
        setShowAddForm(false);
      }
    } catch (error) {
      console.error('Failed to add bookmark:', error);
    }
  };

  const deleteBookmark = async (bookmarkId: string) => {
    try {
      const response = await api.bookmarks.delete(bookmarkId);
      if (response.success) {
        setBookmarks(prev => prev.filter(b => b.id !== bookmarkId));
      }
    } catch (error) {
      console.error('Failed to delete bookmark:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-[500px] max-h-[70vh] flex flex-col">
        {/* 头部 */}
        <div className="p-4 border-b">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">书签管理</h3>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              ✕
            </button>
          </div>
          
          <div className="mt-3">
            {!showAddForm ? (
              <button
                onClick={() => setShowAddForm(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
              >
                添加书签
              </button>
            ) : (
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newNote}
                  onChange={(e) => setNewNote(e.target.value)}
                  placeholder="书签备注（可选）"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded text-sm"
                />
                <button
                  onClick={addBookmark}
                  className="px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 text-sm"
                >
                  确定
                </button>
                <button
                  onClick={() => {
                    setShowAddForm(false);
                    setNewNote('');
                  }}
                  className="px-3 py-2 border border-gray-300 rounded hover:bg-gray-50 text-sm"
                >
                  取消
                </button>
              </div>
            )}
          </div>
        </div>

        {/* 书签列表 */}
        <div className="flex-1 overflow-y-auto p-4">
          {loading && (
            <div className="text-center text-gray-500 py-8">
              加载中...
            </div>
          )}
          
          {!loading && bookmarks.length === 0 && (
            <div className="text-center text-gray-500 py-8">
              暂无书签
            </div>
          )}
          
          {!loading && bookmarks.length > 0 && (
            <div className="space-y-3">
              {bookmarks.map((bookmark) => (
                <div
                  key={bookmark.id}
                  className="p-3 border border-gray-200 rounded hover:bg-gray-50"
                >
                  <div className="flex justify-between items-start">
                    <div 
                      className="flex-1 cursor-pointer"
                      onClick={() => onJumpToBookmark(bookmark.chapter.orderIndex, bookmark.position)}
                    >
                      <div className="font-medium text-blue-600 hover:text-blue-800">
                        {bookmark.chapter.title}
                      </div>
                      {bookmark.note && (
                        <div className="text-sm text-gray-600 mt-1">
                          {bookmark.note}
                        </div>
                      )}
                      <div className="text-xs text-gray-400 mt-1">
                        {formatDate(bookmark.createdAt)}
                      </div>
                    </div>
                    <button
                      onClick={() => deleteBookmark(bookmark.id)}
                      className="text-red-500 hover:text-red-700 text-sm ml-2"
                      title="删除书签"
                    >
                      删除
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Hook for bookmark functionality
export function useBookmarks(bookId: string) {
  const [isBookmarkOpen, setIsBookmarkOpen] = useState(false);

  const openBookmarks = () => setIsBookmarkOpen(true);
  const closeBookmarks = () => setIsBookmarkOpen(false);

  return {
    isBookmarkOpen,
    openBookmarks,
    closeBookmarks
  };
}

'use client';

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Languages, Settings, Eye, EyeOff, RefreshCw, Loader } from 'lucide-react';
import { api } from '@/lib/api';
import { useDebouncedTranslation } from '@/hooks/useDebouncedTranslation';
import type { Language } from '@ebook-platform/types';

interface ImmersiveTranslationProps {
  content: string;
  sourceLanguage: string;
  targetLanguage: string;
  onLanguageChange?: (source: string, target: string) => void;
  className?: string;
}

export const ImmersiveTranslation: React.FC<ImmersiveTranslationProps> = ({
  content,
  sourceLanguage,
  targetLanguage,
  onLanguageChange,
  className = ''
}) => {
  const [languages, setLanguages] = useState<Language[]>([]);
  const [showSettings, setShowSettings] = useState(false);
  const [currentSourceLang, setCurrentSourceLang] = useState(sourceLanguage);
  const [currentTargetLang, setCurrentTargetLang] = useState(targetLanguage);
  const [showTranslation, setShowTranslation] = useState(true);
  const [translationProgress, setTranslationProgress] = useState(0);

  // 使用防抖翻译Hook
  const { 
    translation, 
    loading, 
    error, 
    translate, 
    retranslate 
  } = useDebouncedTranslation({
    defaultDelay: 1000, // 沉浸式翻译使用较长延迟
    context: '沉浸式翻译模式'
  });

  // 获取支持的语言列表
  useEffect(() => {
    const fetchLanguages = async () => {
      try {
        const response = await api.translation.getLanguages();
        if (response.success) {
          setLanguages(response.data || []);
        }
      } catch (err) {
        console.error('获取语言列表失败:', err);
      }
    };
    fetchLanguages();
  }, []);

  // 分段翻译内容
  const segments = useMemo(() => {
    if (!content) return [];
    
    // 按段落分割内容
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim());
    
    // 进一步按句子分割长段落
    const segments: string[] = [];
    paragraphs.forEach(paragraph => {
      if (paragraph.length > 500) {
        // 长段落按句子分割
        const sentences = paragraph.split(/[.!?。！？]\s+/).filter(s => s.trim());
        segments.push(...sentences.map(s => s.trim() + (s.match(/[.!?。！？]$/) ? '' : '。')));
      } else {
        segments.push(paragraph.trim());
      }
    });
    
    return segments;
  }, [content]);

  // 翻译整个内容
  useEffect(() => {
    if (content && currentSourceLang && currentTargetLang) {
      translate(content, currentSourceLang, currentTargetLang);
    }
  }, [content, currentSourceLang, currentTargetLang]);

  // 语言变化时通知父组件
  useEffect(() => {
    if (onLanguageChange) {
      onLanguageChange(currentSourceLang, currentTargetLang);
    }
  }, [currentSourceLang, currentTargetLang]);

  // 获取翻译后的段落
  const translatedSegments = useMemo(() => {
    if (!translation?.translatedText) return [];

    // 按段落分割翻译结果
    const translatedParagraphs = translation.translatedText.split(/\n\s*\n/).filter(p => p.trim());

    // 智能段落对齐算法
    const alignTranslationSegments = (original: string[], translated: string[]): string[] => {
      if (original.length === translated.length) {
        return translated;
      }

      const aligned: string[] = [];

      if (translated.length === 1) {
        // 如果只有一个翻译段落，将其分配给第一个原文段落，其余显示等待状态
        aligned.push(translated[0]);
        for (let i = 1; i < original.length; i++) {
          aligned.push('翻译中...');
        }
      } else if (translated.length < original.length) {
        // 翻译段落少于原文段落，使用不重复的分配策略
        const usedIndices = new Set<number>();

        original.forEach((_, index) => {
          const ratio = translated.length / original.length;
          let translatedIndex = Math.floor(index * ratio);

          // 确保不重复使用同一个翻译段落（除非是最后一个）
          while (usedIndices.has(translatedIndex) && translatedIndex < translated.length - 1) {
            translatedIndex++;
          }

          if (translatedIndex < translated.length) {
            aligned.push(translated[translatedIndex]);
            usedIndices.add(translatedIndex);
          } else {
            aligned.push('翻译中...');
          }
        });
      } else {
        // 翻译段落多于原文段落，合并相邻的翻译段落
        original.forEach((_, index) => {
          const segmentsPerOriginal = translated.length / original.length;
          const startIndex = Math.floor(index * segmentsPerOriginal);
          const endIndex = Math.floor((index + 1) * segmentsPerOriginal);

          const segmentsToMerge = translated.slice(startIndex, endIndex);
          if (segmentsToMerge.length > 0) {
            aligned.push(segmentsToMerge.join('\n\n'));
          } else {
            aligned.push(translated[startIndex] || '翻译中...');
          }
        });
      }

      return aligned;
    };

    return alignTranslationSegments(segments, translatedParagraphs);
  }, [translation, segments]);

  const getLanguageName = (code: string) => {
    const lang = languages.find(l => l.code === code);
    return lang ? `${lang.flag} ${lang.name}` : code;
  };

  // 移除了导致无限循环的调试日志

  return (
    <div className={`immersive-translation h-full flex flex-col ${className}`}>
      {/* 调试信息 */}
      <div className="bg-yellow-100 p-2 text-xs text-yellow-800">
        调试: 内容长度={content?.length || 0}, 加载中={String(loading)}, 错误={error || '无'}
      </div>

      {/* 控制栏 */}
      <div className="sticky top-0 z-10 bg-white border-b border-gray-200 p-4 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Languages className="h-5 w-5 text-indigo-600" />
            <span className="font-medium text-gray-900">沉浸式翻译</span>
            {loading && (
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <Loader className="h-4 w-4 animate-spin" />
                <span>翻译中...</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowTranslation(!showTranslation)}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
              title={showTranslation ? '隐藏翻译' : '显示翻译'}
            >
              {showTranslation ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
            
            <button
              onClick={() => retranslate(content, currentSourceLang, currentTargetLang)}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
              title="重新翻译"
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </button>
            
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
              title="翻译设置"
            >
              <Settings className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* 设置面板 */}
        <AnimatePresence>
          {showSettings && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="mt-4 pt-4 border-t border-gray-100 overflow-hidden"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    源语言
                  </label>
                  <select
                    value={currentSourceLang}
                    onChange={(e) => setCurrentSourceLang(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  >
                    {languages.map(lang => (
                      <option key={lang.code} value={lang.code}>
                        {lang.flag} {lang.name}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    目标语言
                  </label>
                  <select
                    value={currentTargetLang}
                    onChange={(e) => setCurrentTargetLang(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  >
                    {languages.filter(lang => lang.code !== 'auto').map(lang => (
                      <option key={lang.code} value={lang.code}>
                        {lang.flag} {lang.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* 双栏内容 */}
      <div className="flex-1 overflow-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 min-h-full">
          {/* 原文栏 */}
          <div className="p-6 border-r border-gray-200">
            <div className="sticky top-0 bg-white pb-3 mb-4 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <span>原文</span>
                <span className="text-sm text-gray-500">
                  {getLanguageName(currentSourceLang)}
                </span>
              </h3>
            </div>
            
            <div className="space-y-4">
              {segments.map((segment, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="p-4 bg-gray-50 rounded-lg leading-relaxed"
                >
                  {segment}
                </motion.div>
              ))}
            </div>
          </div>

          {/* 翻译栏 */}
          <div className={`p-6 transition-opacity duration-300 ${showTranslation ? 'opacity-100' : 'opacity-30'}`}>
            <div className="sticky top-0 bg-white pb-3 mb-4 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <span>翻译</span>
                <span className="text-sm text-gray-500">
                  {getLanguageName(currentTargetLang)}
                </span>
              </h3>
            </div>
            
            <div className="space-y-4">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <Loader className="h-8 w-8 animate-spin text-indigo-600 mx-auto mb-3" />
                    <p className="text-gray-600">AI正在翻译中...</p>
                  </div>
                </div>
              ) : translatedSegments.length > 0 ? (
                translatedSegments.map((segment, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 + 0.2 }}
                    className="p-4 bg-indigo-50 rounded-lg leading-relaxed"
                  >
                    {segment}
                  </motion.div>
                ))
              ) : (
                <div className="flex items-center justify-center py-12">
                  <p className="text-gray-500">等待翻译...</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

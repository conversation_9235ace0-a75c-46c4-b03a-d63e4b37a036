{"name": "@ebook-platform/web", "version": "0.1.0", "private": true, "description": "电子书平台前端应用", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next out"}, "dependencies": {"@ebook-platform/types": "file:../../packages/types", "@ebook-platform/utils": "file:../../packages/utils", "@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "axios": "^1.6.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^10.16.0", "lucide-react": "^0.292.0", "next": "^14.0.0", "postcss": "^8.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.0", "react-markdown": "^10.1.0", "react-window": "^1.8.0", "react-window-infinite-loader": "^1.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.2.0", "zustand": "^4.4.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/react-window": "^1.8.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0"}}
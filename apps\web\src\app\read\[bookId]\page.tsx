"use client";

import { useEffect, useMemo, useRef, useState } from 'react';
import { api } from '@/lib/api';
import type { Book, Chapter } from '@ebook-platform/types';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import ReadingSettingsPanel, { useReadingSettings } from '@/components/reading/ReadingSettings';
import SearchPanel, { useSearch } from '@/components/reading/SearchPanel';
import BookmarkPanel, { useBookmarks } from '@/components/reading/BookmarkPanel';
import ChapterSummary from '@/components/reading/ChapterSummary';
import { ProgressTracker } from '@/components/reading/ProgressTracker';
import { useReadingProgress } from '@/hooks/useReadingProgress';
import { TranslationPopup } from '@/components/reading/TranslationPopup';
import { ImmersiveTranslation } from '@/components/reading/ImmersiveTranslation';
import { useTextSelection } from '@/hooks/useTextSelection';
import { useTranslationSettings } from '@/hooks/useTranslationSettings';

export default function ReadPage({ params }: { params: { bookId: string } }) {
  const { bookId } = params;
  const [book, setBook] = useState<Book | null>(null);
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showSettings, setShowSettings] = useState<boolean>(false);
  const didInit = useRef(false);
  const LOCAL_KEY = useMemo(() => `reading_progress_${bookId}`,[bookId]);
  const SCROLL_KEY = useMemo(() => (idx: number) => `reading_scroll_${bookId}_${idx}`, [bookId]);

  // 阅读设置
  const { settings, updateSettings, getThemeClasses, getSidebarThemeClasses, getContentStyles } = useReadingSettings(bookId);

  // 搜索功能
  const { isSearchOpen, openSearch, closeSearch } = useSearch();

  // 引用：正文容器和目录项（需要在使用前声明）
  const mainRef = useRef<HTMLDivElement | null>(null);
  const itemRefs = useRef<Array<HTMLButtonElement | null>>([]);

  // 书签功能
  const { isBookmarkOpen, openBookmarks, closeBookmarks } = useBookmarks(bookId);

  // 阅读进度追踪
  const { progress, updateProgress, lastSaveTime } = useReadingProgress({ bookId });
  const [currentPosition, setCurrentPosition] = useState(0);

  // 翻译功能
  const {
    settings: translationSettings,
    updateSettings: updateTranslationSettings,
    reloadSettings: reloadTranslationSettings
  } = useTranslationSettings();
  const [showTranslation, setShowTranslation] = useState(false);

  // 调试翻译设置
  useEffect(() => {
    console.log('🔧 翻译设置状态:', {
      enabled: translationSettings.enabled,
      immersiveMode: translationSettings.immersiveMode,
      shouldShowImmersive: translationSettings.enabled && translationSettings.immersiveMode
    });
  }, [translationSettings.enabled, translationSettings.immersiveMode]);

  // 移除了可能导致无限循环的强制更新逻辑

  // 移除了有问题的页面刷新逻辑

  // 只有在非沉浸式模式下才启用划词翻译
  const { selection, clearSelection } = useTextSelection({
    containerRef: mainRef,
    minLength: 1,
    maxLength: 500,
    onSelectionChange: (selection) => {
      // 只有在启用翻译功能且非沉浸式模式时才显示翻译弹窗
      if (selection && translationSettings.enabled && !translationSettings.immersiveMode) {
        setShowTranslation(true);
      } else {
        setShowTranslation(false);
      }
    }
  });

  // 简易防抖
  const debounce = <T extends (...args: any[]) => void>(fn: T, wait = 300) => {
    let t: any;
    return (...args: Parameters<T>) => {
      clearTimeout(t);
      t = setTimeout(() => fn(...args), wait);
    };
  };

  const goPrev = () => setCurrentIndex(i => Math.max(0, i - 1));
  const goNext = () => setCurrentIndex(i => Math.min(chapters.length - 1, i + 1));

  // 避免开发模式二次执行
  useEffect(() => {
    if (didInit.current) return;
    didInit.current = true;
    (async () => {
      try {
        setLoading(true);
        const res = await api.books.get(bookId);
        if (res.success && res.data) {
          setBook(res.data as any);
          const chs = (res.data as any).chapters || [];
          const sorted = [...chs].sort((a, b) => a.orderIndex - b.orderIndex);
          setChapters(sorted);
        } else {
          setError(res.error || '无法获取书籍信息');
        }
      } catch (e: any) {
        setError(e?.message || '加载失败');
      } finally {
        setLoading(false);
      }
    })();
  }, [bookId]);

  // 首次装载后，优先从服务器进度恢复，其次从本地恢复
  useEffect(() => {
    (async () => {
      if (chapters.length === 0) return;
      try {
        const res = await api.progress.get(bookId);
        if (res.success && typeof (res.data as any)?.currentChapter === 'number') {
          const idx = (res.data as any).currentChapter as number;
          if (idx >= 0 && idx < chapters.length) {
            setCurrentIndex(idx);
            return;
          }
        }
      } catch {}
      // fallback 本地
      try {
        const raw = localStorage.getItem(LOCAL_KEY);
        if (raw) {
          const parsed = JSON.parse(raw);
          const idx = Number(parsed?.index);
          if (Number.isFinite(idx) && idx >= 0 && idx < chapters.length) {
            setCurrentIndex(idx);
            return;
          }
        }
      } catch {}
      setCurrentIndex(0);
    })();
  }, [chapters.length, LOCAL_KEY, bookId]);

  // 加载章节内容 + 保存进度（本地+服务器）
  useEffect(() => {
    (async () => {
      if (!bookId || chapters.length === 0) return;
      try {
        setLoading(true);
        // 保存进度到本地
        try {
          localStorage.setItem(LOCAL_KEY, JSON.stringify({ index: currentIndex, ts: Date.now() }));
        } catch {}
        // 同步到服务器（忽略错误）
        try {
          await api.progress.update(bookId, { currentChapter: currentIndex, currentPosition: 0, percentage: (currentIndex+1)/(chapters.length) });
        } catch {}

        const res = await api.chapters.content(bookId, currentIndex);
        if (res.success) {
          setContent(res.data || '');
        } else {
          setError(res.error || '无法获取章节内容');
        }
      } catch (e: any) {
        setError(e?.message || '加载失败');
      } finally {
        setLoading(false);
      }
    })();
  }, [bookId, currentIndex, chapters.length, LOCAL_KEY]);

  // 章节变化时，目录滚动到当前项居中
  useEffect(() => {
    const el = itemRefs.current[currentIndex];
    if (el && el.scrollIntoView) {
      el.scrollIntoView({ block: 'center', behavior: 'smooth' });
    }
  }, [currentIndex]);

  // 章节变化时，恢复正文滚动位置
  useEffect(() => {
    const key = SCROLL_KEY(currentIndex);
    const saved = localStorage.getItem(key);
    const y = saved ? parseInt(saved, 10) : 0;
    if (mainRef.current) {
      mainRef.current.scrollTop = isFinite(y) ? y : 0;
    }
  }, [currentIndex, SCROLL_KEY]);

  // 正文滚动时保存位置（节流/防抖）
  useEffect(() => {
    const onScroll = debounce(() => {
      if (mainRef.current) {
        const y = mainRef.current.scrollTop;
        try {
          localStorage.setItem(SCROLL_KEY(currentIndex), String(y));
          // 更新当前位置和阅读进度
          setCurrentPosition(y);
          updateProgress(currentIndex, y);
        } catch {}
      }
    }, 250);
    const el = mainRef.current;
    if (!el) return;
    el.addEventListener('scroll', onScroll);
    return () => el.removeEventListener('scroll', onScroll);
  }, [currentIndex, SCROLL_KEY, updateProgress]);

  // 快捷键：Alt+← / Alt+→
  useEffect(() => {
    const handler = (e: KeyboardEvent) => {
      if (e.altKey && e.key === 'ArrowLeft') { e.preventDefault(); goPrev(); }
      if (e.altKey && e.key === 'ArrowRight') { e.preventDefault(); goNext(); }
    };
    window.addEventListener('keydown', handler);
    return () => window.removeEventListener('keydown', handler);
  }, [chapters.length]);

  const onSelectChapter = (index: number) => {
    if (index < 0 || index >= chapters.length) return;
    setCurrentIndex(index);
  };

  const handleJumpToSearchResult = (chapterIndex: number, position: number) => {
    if (chapterIndex !== currentIndex) {
      setCurrentIndex(chapterIndex);
    }
    // 关闭搜索面板
    closeSearch();
    // 滚动到指定位置（简化实现）
    setTimeout(() => {
      if (mainRef.current) {
        const textContent = mainRef.current.textContent || '';
        const lines = textContent.split('\n');
        let currentPos = 0;
        let targetLine = 0;

        for (let i = 0; i < lines.length; i++) {
          if (currentPos + lines[i].length >= position) {
            targetLine = i;
            break;
          }
          currentPos += lines[i].length + 1;
        }

        const lineHeight = parseInt(getComputedStyle(mainRef.current).lineHeight) || 24;
        mainRef.current.scrollTop = targetLine * lineHeight;
      }
    }, 100);
  };

  const handleJumpToBookmark = (chapterIndex: number, position: number) => {
    if (chapterIndex !== currentIndex) {
      setCurrentIndex(chapterIndex);
    }
    closeBookmarks();
    // 滚动到书签位置
    setTimeout(() => {
      if (mainRef.current) {
        mainRef.current.scrollTop = position;
      }
    }, 100);
  };

  // 关闭翻译弹窗
  const handleCloseTranslation = () => {
    setShowTranslation(false);
    clearSelection();
  };

  // 处理沉浸式翻译语言变化
  const handleImmersiveLanguageChange = (source: string, target: string) => {
    updateTranslationSettings({
      immersiveSourceLanguage: source,
      immersiveTargetLanguage: target
    });
  };

  return (
    <div className={`min-h-screen flex ${getThemeClasses()}`}>
      {/* 侧栏：目录 */}
      <aside className={`w-72 border-r p-4 space-y-2 overflow-auto transition-colors ${getSidebarThemeClasses()}`}>
        <div className="flex items-center justify-between">
          <Link href="/" className="text-sm text-blue-600">返回首页</Link>
          <div className="flex items-center space-x-2">
            <button
              onClick={openSearch}
              className="text-xs px-2 py-1 rounded border hover:bg-opacity-10 hover:bg-gray-500"
              title="搜索 (Ctrl+F)"
            >
              搜索
            </button>
            <button
              onClick={openBookmarks}
              className="text-xs px-2 py-1 rounded border hover:bg-opacity-10 hover:bg-gray-500"
              title="书签管理"
            >
              书签
            </button>
            <span className="text-xs opacity-70">{book?.title || '阅读'}</span>
          </div>
        </div>

        {/* 阅读进度 */}
        <div className="mt-3">
          <ProgressTracker
            bookId={bookId}
            currentChapter={currentIndex}
            currentPosition={currentPosition}
            totalChapters={chapters.length}
            className="mb-4"
          />
        </div>

        <div className="mt-3">
          {chapters.map((ch, idx) => (
            <button
              ref={(el) => { itemRefs.current[idx] = el; }}
              key={ch.id || idx}
              onClick={() => onSelectChapter(idx)}
              className={`block w-full text-left px-2 py-1 rounded transition-colors ${
                settings.theme === 'dark'
                  ? `hover:bg-gray-700 ${idx===currentIndex ? 'bg-blue-800 text-blue-200' : ''}`
                  : settings.theme === 'sepia'
                  ? `hover:bg-yellow-200 ${idx===currentIndex ? 'bg-yellow-300 text-yellow-800' : ''}`
                  : `hover:bg-gray-100 ${idx===currentIndex ? 'bg-blue-50 text-blue-700' : ''}`
              }`}
            >
              <span className="text-sm">{ch.orderIndex+1}. {ch.title}</span>
              <span className="ml-2 text-xs text-gray-400">{ch.wordCount}字</span>
            </button>
          ))}
          {chapters.length===0 && <div className="text-xs text-gray-400">暂无目录</div>}
        </div>
      </aside>

      {/* 正文 */}
      <main ref={mainRef} className="flex-1 p-6 overflow-auto relative">
        {/* 右上角设置按钮 */}
        <div className="absolute top-4 right-4 z-50">
          <button
            onClick={() => setShowSettings(true)}
            className={`p-3 rounded-full shadow-lg transition-all hover:scale-110 cursor-pointer ${
              settings.theme === 'dark'
                ? 'bg-gray-800 text-white hover:bg-gray-700'
                : settings.theme === 'sepia'
                ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
            title="阅读设置"
            type="button"
          >
            ⚙️
          </button>
        </div>

        <div className="flex items-center gap-2 mb-4">
          <button
            onClick={goPrev}
            disabled={currentIndex<=0}
            className={`px-3 py-1 rounded border disabled:opacity-50 transition-colors ${
              settings.theme === 'dark'
                ? 'border-gray-600 hover:bg-gray-800 text-gray-200'
                : settings.theme === 'sepia'
                ? 'border-yellow-300 hover:bg-yellow-200 text-yellow-800'
                : 'border-gray-300 hover:bg-gray-50 text-gray-700'
            }`}
          >
            上一章
          </button>
          <button
            onClick={goNext}
            disabled={currentIndex>=chapters.length-1}
            className={`px-3 py-1 rounded border disabled:opacity-50 transition-colors ${
              settings.theme === 'dark'
                ? 'border-gray-600 hover:bg-gray-800 text-gray-200'
                : settings.theme === 'sepia'
                ? 'border-yellow-300 hover:bg-yellow-200 text-yellow-800'
                : 'border-gray-300 hover:bg-gray-50 text-gray-700'
            }`}
          >
            下一章
          </button>
          <span className="text-xs opacity-70 ml-2">快捷键：Alt+← / Alt+→</span>
        </div>

        {loading && <div className="opacity-70">加载中...</div>}
        {error && <div className="text-red-500 text-sm">{error}</div>}
        {!loading && !error && (
          <>
            {/* 调试信息 */}
            <div className="mb-2 p-2 bg-yellow-100 text-xs text-yellow-800 rounded">
              <div>翻译设置: enabled={String(translationSettings.enabled)}, immersiveMode={String(translationSettings.immersiveMode)}</div>
              <div>当前时间={new Date().toLocaleTimeString()}</div>
              <div>localStorage: {localStorage.getItem('translation_settings')?.substring(0, 100)}...</div>
              <div>应该显示沉浸式: {String(translationSettings.enabled && translationSettings.immersiveMode)}</div>
              <button
                onClick={() => window.location.reload()}
                className="mt-1 px-2 py-1 bg-blue-500 text-white text-xs rounded"
              >
                刷新页面
              </button>
              <button
                onClick={() => {
                  console.log('🔧 手动检查localStorage:', localStorage.getItem('translation_settings'));
                  const stored = localStorage.getItem('translation_settings');
                  if (stored) {
                    const parsed = JSON.parse(stored);
                    console.log('🔧 解析结果:', parsed);
                    alert(`localStorage中的immersiveMode: ${parsed.immersiveMode}`);
                  }
                }}
                className="mt-1 ml-2 px-2 py-1 bg-green-500 text-white text-xs rounded"
              >
                检查存储
              </button>
              <button
                onClick={() => {
                  console.log('🔧 手动重新加载翻译设置');
                  reloadTranslationSettings();
                }}
                className="mt-1 ml-2 px-2 py-1 bg-purple-500 text-white text-xs rounded"
              >
                重新加载设置
              </button>
            </div>

            {/* 沉浸式翻译模式 */}
            {translationSettings.enabled && translationSettings.immersiveMode ? (
              <div className="h-full flex flex-col">
                <div className="mb-4">
                  <h1 className="text-xl font-semibold">{chapters[currentIndex]?.title || '正文'}</h1>
                </div>
                <ImmersiveTranslation
                  content={content || '（无内容）'}
                  sourceLanguage={translationSettings.immersiveSourceLanguage}
                  targetLanguage={translationSettings.immersiveTargetLanguage}
                  onLanguageChange={handleImmersiveLanguageChange}
                  className="flex-1"
                />
              </div>
            ) : (
              /* 普通阅读模式 */
              <article className="mx-auto" style={getContentStyles()}>
                <h1 className="text-xl font-semibold mb-4">{chapters[currentIndex]?.title || '正文'}</h1>
                <div className="whitespace-pre-wrap" style={{ lineHeight: settings.lineHeight }}>
                  {content || '（无内容）'}
                </div>
              </article>
            )}
          </>
        )}
      </main>

      {/* 搜索面板 */}
      <SearchPanel
        isOpen={isSearchOpen}
        onClose={closeSearch}
        bookId={bookId}
        chapters={chapters}
        currentChapterIndex={currentIndex}
        currentContent={content}
        onJumpToResult={handleJumpToSearchResult}
      />

      {/* 书签面板 */}
      <BookmarkPanel
        isOpen={isBookmarkOpen}
        onClose={closeBookmarks}
        bookId={bookId}
        currentChapterIndex={currentIndex}
        currentChapterId={chapters[currentIndex]?.id}
        onJumpToBookmark={handleJumpToBookmark}
      />

      {/* 阅读设置面板 */}
      <ReadingSettingsPanel
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        settings={settings}
        onSettingsChange={updateSettings}
      />

      {/* AI章节总结 */}
      <ChapterSummary
        bookId={bookId}
        currentChapterIndex={currentIndex}
        chapterTitle={chapters[currentIndex]?.title || '正文'}
        theme={settings.theme}
      />

      {/* 划词翻译弹窗（仅在非沉浸式模式下显示） */}
      {showTranslation && selection && !translationSettings.immersiveMode && (
        <TranslationPopup
          selectedText={selection.text}
          position={selection.position}
          onClose={handleCloseTranslation}
          sourceLanguage={translationSettings.defaultSourceLanguage}
          targetLanguage={translationSettings.defaultTargetLanguage}
          context={`书籍：${book?.title || ''}，章节：${chapters[currentIndex]?.title || ''}`}
        />
      )}

    </div>
  );
}


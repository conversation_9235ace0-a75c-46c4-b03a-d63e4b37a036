// 文件处理工具
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const validateTextFile = (file: File): boolean => {
  const allowedTypes = ['text/plain', 'application/octet-stream'];
  const allowedExtensions = ['.txt'];
  
  const hasValidType = allowedTypes.includes(file.type);
  const hasValidExtension = allowedExtensions.some(ext => 
    file.name.toLowerCase().endsWith(ext)
  );
  
  return hasValidType || hasValidExtension;
};

// 文本处理工具
export const detectChapters = (content: string): Array<{title: string, position: number}> => {
  const chapterPatterns = [
    /^第[一二三四五六七八九十百千万\d]+章\s*.*/gm,
    /^第[一二三四五六七八九十百千万\d]+节\s*.*/gm,
    /^Chapter\s+\d+.*/gim,
    /^\d+\.\s*.*/gm,
    /^[一二三四五六七八九十百千万]+、.*/gm
  ];
  
  const chapters: Array<{title: string, position: number}> = [];
  
  for (const pattern of chapterPatterns) {
    const matches = Array.from(content.matchAll(pattern));
    console.log(`🔍 模式 ${pattern} 匹配到 ${matches.length} 个结果`);
    if (matches.length > 0) {
      matches.forEach((match, index) => {
        console.log(`  ${index + 1}. "${match[0].trim()}" (位置: ${match.index})`);
      });
    }

    if (matches.length >= 2) { // 至少要有2个匹配才认为是章节（降低要求）
      matches.forEach(match => {
        if (match.index !== undefined) {
          chapters.push({
            title: match[0].trim(),
            position: match.index
          });
        }
      });
      break; // 找到一种模式就停止
    }
  }
  
  return chapters.sort((a, b) => a.position - b.position);
};

export const cleanText = (text: string): string => {
  return text
    .replace(/\r\n/g, '\n') // 统一换行符
    .replace(/\n{3,}/g, '\n\n') // 合并多余空行
    .replace(/[ \t]+/g, ' ') // 合并多余空格
    .trim();
};

// 阅读进度计算
export const calculateReadingProgress = (
  currentPosition: number,
  totalLength: number
): number => {
  if (totalLength === 0) return 0;
  return Math.min(Math.max((currentPosition / totalLength) * 100, 0), 100);
};

// 时间格式化
export const formatReadingTime = (minutes: number): string => {
  if (minutes < 60) {
    return `${Math.round(minutes)}分钟`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = Math.round(minutes % 60);
  
  if (hours < 24) {
    return remainingMinutes > 0 
      ? `${hours}小时${remainingMinutes}分钟`
      : `${hours}小时`;
  }
  
  const days = Math.floor(hours / 24);
  const remainingHours = hours % 24;
  
  return remainingHours > 0
    ? `${days}天${remainingHours}小时`
    : `${days}天`;
};

// 字符串工具
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
};

export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

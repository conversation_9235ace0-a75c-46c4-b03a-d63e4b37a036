import { FastifyRequest, FastifyReply } from 'fastify';
import { extractTokenFromRequest, verifyToken } from '../lib/auth.js';

/**
 * 认证中间件 - 验证JWT token
 */
export async function authenticateToken(request: FastifyRequest, reply: FastifyReply) {
  try {
    const token = extractTokenFromRequest(request);
    
    if (!token) {
      return reply.code(401).send({
        success: false,
        error: 'MISSING_TOKEN',
        message: '缺少认证token'
      });
    }
    
    const payload = verifyToken(token);
    
    if (!payload) {
      return reply.code(401).send({
        success: false,
        error: 'INVALID_TOKEN',
        message: 'token无效或已过期'
      });
    }
    
    // 将用户信息附加到请求对象
    request.user = payload;
    
  } catch (error) {
    console.error('认证中间件错误:', error);
    return reply.code(500).send({
      success: false,
      error: 'AUTH_ERROR',
      message: '认证过程中发生错误'
    });
  }
}

/**
 * 可选认证中间件 - 如果有token则验证，没有则跳过
 */
export async function optionalAuth(request: FastifyRequest, reply: FastifyReply) {
  try {
    const token = extractTokenFromRequest(request);
    
    if (token) {
      const payload = verifyToken(token);
      if (payload) {
        request.user = payload;
      }
    }
    
    // 无论是否有token都继续执行
  } catch (error) {
    console.error('可选认证中间件错误:', error);
    // 不阻止请求继续执行
  }
}

/**
 * 管理员权限中间件（预留）
 */
export async function requireAdmin(request: FastifyRequest, reply: FastifyReply) {
  // 首先确保用户已认证
  await authenticateToken(request, reply);
  
  if (reply.sent) {
    return; // 如果认证失败，已经发送响应
  }
  
  // 这里可以添加管理员权限检查逻辑
  // 例如检查用户角色、权限等
  
  // 目前所有认证用户都有管理员权限
  // 后续可以扩展用户角色系统
}

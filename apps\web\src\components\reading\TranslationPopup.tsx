'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Languages, Copy, Volume2, Settings, X, RefreshCw } from 'lucide-react';
import { api } from '@/lib/api';
import { useDebouncedTranslation } from '@/hooks/useDebouncedTranslation';
import type { Language, TranslationResponse } from '@ebook-platform/types';

interface TranslationPopupProps {
  selectedText: string;
  position: { x: number; y: number };
  onClose: () => void;
  sourceLanguage?: string;
  targetLanguage?: string;
  context?: string;
}

export const TranslationPopup: React.FC<TranslationPopupProps> = ({
  selectedText,
  position,
  onClose,
  sourceLanguage = 'auto',
  targetLanguage = 'zh-CN',
  context
}) => {
  const [languages, setLanguages] = useState<Language[]>([]);
  const [currentSourceLang, setCurrentSourceLang] = useState(sourceLanguage);
  const [currentTargetLang, setCurrentTargetLang] = useState(targetLanguage);
  const [showSettings, setShowSettings] = useState(false);
  const popupRef = useRef<HTMLDivElement>(null);

  // 使用防抖翻译Hook
  const {
    translation,
    loading,
    error,
    translate,
    retranslate,
    clearTranslation
  } = useDebouncedTranslation({
    defaultDelay: 800,
    context
  });

  // 获取支持的语言列表
  useEffect(() => {
    const fetchLanguages = async () => {
      try {
        const response = await api.translation.getLanguages();
        if (response.success) {
          setLanguages(response.data || []);
        }
      } catch (err) {
        console.error('获取语言列表失败:', err);
      }
    };
    fetchLanguages();
  }, []);

  // 组件卸载时清理翻译
  useEffect(() => {
    return () => {
      clearTranslation();
    };
  }, [clearTranslation]);

  // 初始翻译
  useEffect(() => {
    if (selectedText) {
      // 首次翻译使用较短的延迟
      translate(selectedText, currentSourceLang, currentTargetLang, 300);
    }
  }, [selectedText, translate]);

  // 语言切换时的翻译
  useEffect(() => {
    if (selectedText && translation) {
      // 语言切换时使用标准延迟
      translate(selectedText, currentSourceLang, currentTargetLang, 500);
    }
  }, [currentSourceLang, currentTargetLang, selectedText, translation, translate]);

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popupRef.current && !popupRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);



  // 复制翻译结果
  const copyTranslation = async () => {
    if (translation?.translatedText) {
      try {
        await navigator.clipboard.writeText(translation.translatedText);
        // 可以添加一个简单的提示
      } catch (err) {
        console.error('复制失败:', err);
      }
    }
  };

  // 语音播放（如果浏览器支持）
  const playAudio = (text: string, lang: string) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = lang;
      speechSynthesis.speak(utterance);
    }
  };

  // 计算弹窗位置
  const getPopupStyle = () => {
    const popupWidth = 320;
    const popupHeight = 200;
    const margin = 10;

    let x = position.x;
    let y = position.y + 20; // 在选中文本下方

    // 防止超出右边界
    if (x + popupWidth > window.innerWidth - margin) {
      x = window.innerWidth - popupWidth - margin;
    }

    // 防止超出左边界
    if (x < margin) {
      x = margin;
    }

    // 防止超出下边界
    if (y + popupHeight > window.innerHeight - margin) {
      y = position.y - popupHeight - 10; // 在选中文本上方
    }

    // 防止超出上边界
    if (y < margin) {
      y = margin;
    }

    return {
      left: x,
      top: y
    };
  };

  const getLanguageName = (code: string) => {
    const lang = languages.find(l => l.code === code);
    return lang ? `${lang.flag} ${lang.name}` : code;
  };

  return (
    <AnimatePresence>
      <motion.div
        ref={popupRef}
        initial={{ opacity: 0, scale: 0.9, y: 10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 10 }}
        className="fixed z-50 bg-white rounded-lg shadow-xl border border-gray-200 max-w-sm"
        style={getPopupStyle()}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-3 border-b border-gray-100">
          <div className="flex items-center gap-2">
            <Languages className="h-4 w-4 text-indigo-600" />
            <span className="text-sm font-medium text-gray-900">翻译</span>
          </div>
          <div className="flex items-center gap-1">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
              title="翻译设置"
            >
              <Settings className="h-4 w-4" />
            </button>
            <button
              onClick={onClose}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
              title="关闭"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* 设置面板 */}
        <AnimatePresence>
          {showSettings && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="border-b border-gray-100 overflow-hidden"
            >
              <div className="p-3 space-y-3">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">源语言</label>
                  <select
                    value={currentSourceLang}
                    onChange={(e) => setCurrentSourceLang(e.target.value)}
                    className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:ring-1 focus:ring-indigo-500 focus:border-transparent"
                  >
                    {languages.map(lang => (
                      <option key={lang.code} value={lang.code}>
                        {lang.flag} {lang.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">目标语言</label>
                  <select
                    value={currentTargetLang}
                    onChange={(e) => setCurrentTargetLang(e.target.value)}
                    className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:ring-1 focus:ring-indigo-500 focus:border-transparent"
                  >
                    {languages.filter(lang => lang.code !== 'auto').map(lang => (
                      <option key={lang.code} value={lang.code}>
                        {lang.flag} {lang.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 原文 */}
        <div className="p-3 border-b border-gray-100">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs text-gray-500">
              {getLanguageName(translation?.sourceLanguage || currentSourceLang)}
            </span>
            <button
              onClick={() => playAudio(selectedText, translation?.sourceLanguage || currentSourceLang)}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
              title="朗读原文"
            >
              <Volume2 className="h-3 w-3" />
            </button>
          </div>
          <p className="text-sm text-gray-900 leading-relaxed">
            {selectedText}
          </p>
        </div>

        {/* 翻译结果 */}
        <div className="p-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs text-gray-500">
              {getLanguageName(currentTargetLang)}
            </span>
            <div className="flex items-center gap-1">
              {translation && (
                <>
                  <button
                    onClick={() => playAudio(translation.translatedText, currentTargetLang)}
                    className="p-1 text-gray-400 hover:text-gray-600 rounded"
                    title="朗读翻译"
                  >
                    <Volume2 className="h-3 w-3" />
                  </button>
                  <button
                    onClick={copyTranslation}
                    className="p-1 text-gray-400 hover:text-gray-600 rounded"
                    title="复制翻译"
                  >
                    <Copy className="h-3 w-3" />
                  </button>
                </>
              )}
              <button
                onClick={() => retranslate(selectedText, currentSourceLang, currentTargetLang)}
                className="p-1 text-gray-400 hover:text-gray-600 rounded"
                title="重新翻译"
                disabled={loading}
              >
                <RefreshCw className={`h-3 w-3 ${loading ? 'animate-spin' : ''}`} />
              </button>
            </div>
          </div>

          {loading ? (
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
              <span>AI翻译中...</span>
            </div>
          ) : error ? (
            <div className="text-sm text-red-600">
              {error}
            </div>
          ) : translation ? (
            <p className="text-sm text-gray-900 leading-relaxed">
              {translation.translatedText}
            </p>
          ) : (
            <div className="text-sm text-gray-500">
              等待翻译...
            </div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

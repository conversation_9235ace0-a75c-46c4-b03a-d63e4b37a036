import NodeCache from 'node-cache';

// 创建不同用途的缓存实例
export const caches = {
  // 书籍内容缓存 (TTL: 1小时)
  books: new NodeCache({ 
    stdTTL: 3600, 
    checkperiod: 600,
    maxKeys: 100 
  }),
  
  // 章节内容缓存 (TTL: 30分钟)
  chapters: new NodeCache({ 
    stdTTL: 1800, 
    checkperiod: 300,
    maxKeys: 500 
  }),
  
  // AI生成内容缓存 (TTL: 24小时)
  ai: new NodeCache({ 
    stdTTL: 86400, 
    checkperiod: 3600,
    maxKeys: 200 
  }),
  
  // 翻译结果缓存 (TTL: 12小时)
  translation: new NodeCache({ 
    stdTTL: 43200, 
    checkperiod: 1800,
    maxKeys: 1000 
  }),
  
  // 用户会话缓存 (TTL: 2小时)
  session: new NodeCache({ 
    stdTTL: 7200, 
    checkperiod: 600,
    maxKeys: 50 
  })
};

// 缓存键生成器
export const cacheKeys = {
  book: (bookId: string) => `book:${bookId}`,
  bookContent: (bookId: string) => `book_content:${bookId}`,
  chapter: (chapterId: string) => `chapter:${chapterId}`,
  chapterContent: (bookId: string, chapterIndex: number) => `chapter_content:${bookId}:${chapterIndex}`,
  chapterSummary: (chapterId: string) => `summary:${chapterId}`,
  translation: (text: string, targetLang: string) => `translation:${Buffer.from(text).toString('base64').slice(0, 50)}:${targetLang}`,
  userProgress: (userId: string, bookId: string) => `progress:${userId}:${bookId}`,
  userBookmarks: (userId: string, bookId: string) => `bookmarks:${userId}:${bookId}`
};

// 缓存工具函数
export const cacheUtils = {
  // 获取缓存
  get: <T>(cache: NodeCache, key: string): T | undefined => {
    return cache.get<T>(key);
  },
  
  // 设置缓存
  set: <T>(cache: NodeCache, key: string, value: T, ttl?: number): boolean => {
    return typeof ttl === 'number' ? cache.set(key, value, ttl) : cache.set(key, value);
  },
  
  // 删除缓存
  del: (cache: NodeCache, key: string): number => {
    return cache.del(key);
  },
  
  // 清空特定缓存
  flush: (cache: NodeCache): void => {
    cache.flushAll();
  },
  
  // 获取缓存统计
  getStats: (cache: NodeCache) => {
    return cache.getStats();
  },
  
  // 批量删除缓存（通过前缀）
  delByPrefix: (cache: NodeCache, prefix: string): number => {
    const keys = cache.keys().filter(key => key.startsWith(prefix));
    return cache.del(keys);
  }
};

// 缓存中间件
export const cacheMiddleware = {
  // 书籍内容缓存中间件
  bookContent: (bookId: string) => {
    return {
      get: () => cacheUtils.get(caches.books, cacheKeys.bookContent(bookId)),
      set: (content: any, ttl?: number) => cacheUtils.set(caches.books, cacheKeys.bookContent(bookId), content, ttl),
      del: () => cacheUtils.del(caches.books, cacheKeys.bookContent(bookId))
    };
  },
  
  // 章节内容缓存中间件
  chapterContent: (bookId: string, chapterIndex: number) => {
    return {
      get: () => cacheUtils.get(caches.chapters, cacheKeys.chapterContent(bookId, chapterIndex)),
      set: (content: any, ttl?: number) => cacheUtils.set(caches.chapters, cacheKeys.chapterContent(bookId, chapterIndex), content, ttl),
      del: () => cacheUtils.del(caches.chapters, cacheKeys.chapterContent(bookId, chapterIndex))
    };
  }
};

// 清理过期缓存的定时任务
setInterval(() => {
  Object.values(caches).forEach(cache => {
    const stats = cache.getStats();
    console.log(`Cache stats - Keys: ${stats.keys}, Hits: ${stats.hits}, Misses: ${stats.misses}`);
  });
}, 300000); // 每5分钟输出一次统计信息

'use client';

import { useState, useEffect, useCallback } from 'react';
import type { TranslationSettings } from '@ebook-platform/types';

const DEFAULT_SETTINGS: TranslationSettings = {
  enabled: true,
  defaultSourceLanguage: 'auto',
  defaultTargetLanguage: 'zh-CN',
  autoDetectLanguage: true,
  showOriginalText: true,
  immersiveMode: false,
  immersiveSourceLanguage: 'en',
  immersiveTargetLanguage: 'zh-CN'
};

const STORAGE_KEY = 'translation_settings';

interface UseTranslationSettingsReturn {
  settings: TranslationSettings;
  updateSettings: (newSettings: Partial<TranslationSettings>) => void;
  resetSettings: () => void;
  reloadSettings: () => void;
  isLoading: boolean;
}

export const useTranslationSettings = (): UseTranslationSettingsReturn => {
  const [settings, setSettings] = useState<TranslationSettings>(DEFAULT_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);

  // 从本地存储加载设置
  const loadSettings = useCallback(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      console.log('🔧 从localStorage加载设置:', stored);

      if (stored) {
        const parsedSettings = JSON.parse(stored);
        console.log('🔧 解析的设置:', parsedSettings);

        // 只为缺失的字段使用默认值，不覆盖已存在的设置
        const finalSettings: TranslationSettings = {
          enabled: parsedSettings.enabled !== undefined ? parsedSettings.enabled : DEFAULT_SETTINGS.enabled,
          defaultSourceLanguage: parsedSettings.defaultSourceLanguage || DEFAULT_SETTINGS.defaultSourceLanguage,
          defaultTargetLanguage: parsedSettings.defaultTargetLanguage || DEFAULT_SETTINGS.defaultTargetLanguage,
          autoDetectLanguage: parsedSettings.autoDetectLanguage !== undefined ? parsedSettings.autoDetectLanguage : DEFAULT_SETTINGS.autoDetectLanguage,
          showOriginalText: parsedSettings.showOriginalText !== undefined ? parsedSettings.showOriginalText : DEFAULT_SETTINGS.showOriginalText,
          immersiveMode: parsedSettings.immersiveMode !== undefined ? parsedSettings.immersiveMode : DEFAULT_SETTINGS.immersiveMode,
          immersiveSourceLanguage: parsedSettings.immersiveSourceLanguage || DEFAULT_SETTINGS.immersiveSourceLanguage,
          immersiveTargetLanguage: parsedSettings.immersiveTargetLanguage || DEFAULT_SETTINGS.immersiveTargetLanguage
        };
        console.log('🔧 最终设置:', finalSettings);

        setSettings(finalSettings);
      } else {
        console.log('🔧 没有找到存储的设置，使用默认设置');
        setSettings(DEFAULT_SETTINGS);
      }
    } catch (error) {
      console.error('❌ 加载翻译设置失败:', error);
      setSettings(DEFAULT_SETTINGS);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 保存设置到本地存储
  const saveSettings = useCallback((newSettings: TranslationSettings) => {
    try {
      const settingsJson = JSON.stringify(newSettings);
      console.log('🔧 保存设置到localStorage:', STORAGE_KEY, settingsJson);
      localStorage.setItem(STORAGE_KEY, settingsJson);

      // 验证保存是否成功
      const saved = localStorage.getItem(STORAGE_KEY);
      console.log('🔧 验证保存结果:', saved);
    } catch (error) {
      console.error('❌ 保存翻译设置失败:', error);
    }
  }, []);

  // 更新设置 - 添加详细调试
  const updateSettings = useCallback((newSettings: Partial<TranslationSettings>) => {
    console.log('🔧 [Hook] ===== updateSettings 开始 =====');
    console.log('🔧 [Hook] 传入参数:', newSettings);
    console.log('🔧 [Hook] 当前settings状态:', settings);

    setSettings(prevSettings => {
      console.log('🔧 [Hook] setSettings回调执行');
      console.log('🔧 [Hook] prevSettings:', prevSettings);

      if (!prevSettings) {
        console.error('❌ [Hook] prevSettings 为空，使用默认设置');
        return DEFAULT_SETTINGS;
      }

      const updatedSettings = { ...prevSettings, ...newSettings };
      console.log('🔧 [Hook] 计算出的updatedSettings:', updatedSettings);
      console.log('🔧 [Hook] immersiveMode值:', updatedSettings.immersiveMode);

      // 保存前检查localStorage当前内容
      const beforeSave = localStorage.getItem(STORAGE_KEY);
      console.log('🔧 [Hook] 保存前localStorage:', beforeSave);

      // 直接同步保存到localStorage
      try {
        const jsonToSave = JSON.stringify(updatedSettings);
        console.log('🔧 [Hook] 准备保存的JSON:', jsonToSave);

        localStorage.setItem(STORAGE_KEY, jsonToSave);
        console.log('🔧 [Hook] localStorage.setItem 调用完成');

        // 立即读取验证
        const afterSave = localStorage.getItem(STORAGE_KEY);
        console.log('🔧 [Hook] 保存后立即读取:', afterSave);

        if (afterSave === jsonToSave) {
          console.log('✅ [Hook] localStorage保存验证成功');
        } else {
          console.error('❌ [Hook] localStorage保存验证失败');
          console.error('期望:', jsonToSave);
          console.error('实际:', afterSave);
        }
      } catch (error) {
        console.error('❌ [Hook] localStorage保存异常:', error);
      }

      console.log('🔧 [Hook] 返回updatedSettings:', updatedSettings);
      console.log('🔧 [Hook] ===== updateSettings 结束 =====');
      return updatedSettings;
    });
  }, [settings]);

  // 重置设置
  const resetSettings = useCallback(() => {
    setSettings(DEFAULT_SETTINGS);
    saveSettings(DEFAULT_SETTINGS);
  }, [saveSettings]);

  // 初始化时加载设置
  useEffect(() => {
    console.log('🔧 useTranslationSettings 初始化，开始加载设置');
    loadSettings();
  }, [loadSettings]);

  // 移除了可能导致无限循环的事件监听器

  // 调试：监听设置变化
  useEffect(() => {
    console.log('🔧 翻译设置状态变化:', settings);
  }, [settings]);

  return {
    settings,
    updateSettings,
    resetSettings,
    reloadSettings: loadSettings,
    isLoading
  };
};

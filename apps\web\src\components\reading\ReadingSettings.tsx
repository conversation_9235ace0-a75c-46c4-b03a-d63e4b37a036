"use client";

import { useState, useEffect } from 'react';
import { Languages } from 'lucide-react';
import { useTranslationSettings } from '@/hooks/useTranslationSettings';

export interface ReadingSettings {
  fontSize: number;
  lineHeight: number;
  theme: 'light' | 'dark' | 'sepia';
  fontFamily: string;
  maxWidth: number;
}

const DEFAULT_SETTINGS: ReadingSettings = {
  fontSize: 16,
  lineHeight: 1.6,
  theme: 'light',
  fontFamily: 'system-ui',
  maxWidth: 800
};

interface ReadingSettingsProps {
  isOpen: boolean;
  onClose: () => void;
  settings: ReadingSettings;
  onSettingsChange: (settings: ReadingSettings) => void;
}

export default function ReadingSettingsPanel({ isOpen, onClose, settings, onSettingsChange }: ReadingSettingsProps) {
  const {
    settings: translationSettings,
    updateSettings: updateTranslationSettings,
    isLoading: translationLoading
  } = useTranslationSettings();

  // 监听localStorage变化 - 增强版
  useEffect(() => {
    console.log('🔍 [监听器] 开始设置localStorage监听器');

    const originalSetItem = localStorage.setItem;
    const originalGetItem = localStorage.getItem;

    // 重写setItem
    localStorage.setItem = function(key: string, value: string) {
      console.log('🔍 [监听] localStorage.setItem 被调用');
      console.log('🔍 [监听] key:', key);
      console.log('🔍 [监听] value:', value);
      if (key === 'translation_settings') {
        console.log('🔍 [监听] ⚠️ 翻译设置被修改！');
        console.log('🔍 [监听] 调用栈:', new Error().stack);
      }
      return originalSetItem.call(this, key, value);
    };

    // 重写getItem来监听读取
    localStorage.getItem = function(key: string) {
      const result = originalGetItem.call(this, key);
      if (key === 'translation_settings') {
        console.log('🔍 [监听] localStorage.getItem 被调用');
        console.log('🔍 [监听] 返回值:', result);
      }
      return result;
    };

    console.log('🔍 [监听器] localStorage监听器设置完成');

    return () => {
      console.log('🔍 [监听器] 清理localStorage监听器');
      localStorage.setItem = originalSetItem;
      localStorage.getItem = originalGetItem;
    };
  }, []);

  // 安全检查
  if (!translationSettings) {
    console.error('❌ translationSettings 为空');
    return null;
  }

  if (!isOpen) return null;

  const handleChange = (key: keyof ReadingSettings, value: any) => {
    const newSettings = { ...settings, [key]: value };
    onSettingsChange(newSettings);
  };

  const getModalThemeClasses = () => {
    switch (settings.theme) {
      case 'dark':
        return 'bg-gray-800 text-white';
      case 'sepia':
        return 'bg-yellow-50 text-yellow-900';
      default:
        return 'bg-white text-gray-900';
    }
  };

  const getInputThemeClasses = () => {
    switch (settings.theme) {
      case 'dark':
        return 'bg-gray-700 border-gray-600 text-white';
      case 'sepia':
        return 'bg-yellow-100 border-yellow-300 text-yellow-900';
      default:
        return 'bg-white border-gray-300 text-gray-900';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`rounded-lg p-6 w-96 max-h-[80vh] overflow-y-auto shadow-2xl ${getModalThemeClasses()}`}>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">阅读设置</h3>
          <button
            onClick={onClose}
            className={`hover:opacity-70 text-xl ${settings.theme === 'dark' ? 'text-gray-300' : 'text-gray-500'}`}
          >
            ✕
          </button>
        </div>

        <div className="space-y-4">
          {/* 字体大小 */}
          <div>
            <label className="block text-sm font-medium mb-2">字体大小</label>
            <div className="flex items-center space-x-2">
              <span className="text-sm">小</span>
              <input
                type="range"
                min="12"
                max="24"
                value={settings.fontSize}
                onChange={(e) => handleChange('fontSize', parseInt(e.target.value))}
                className="flex-1"
              />
              <span className="text-sm">大</span>
              <span className="text-sm w-8">{settings.fontSize}px</span>
            </div>
          </div>

          {/* 行间距 */}
          <div>
            <label className="block text-sm font-medium mb-2">行间距</label>
            <div className="flex items-center space-x-2">
              <span className="text-sm">紧</span>
              <input
                type="range"
                min="1.2"
                max="2.0"
                step="0.1"
                value={settings.lineHeight}
                onChange={(e) => handleChange('lineHeight', parseFloat(e.target.value))}
                className="flex-1"
              />
              <span className="text-sm">松</span>
              <span className="text-sm w-8">{settings.lineHeight}</span>
            </div>
          </div>

          {/* 主题 */}
          <div>
            <label className="block text-sm font-medium mb-2">主题模式</label>
            <div className="grid grid-cols-3 gap-2">
              {[
                { key: 'light', name: '☀️ 日间', bg: 'bg-white', text: 'text-black', border: 'border-gray-300' },
                { key: 'dark', name: '🌙 夜间', bg: 'bg-gray-900', text: 'text-white', border: 'border-gray-600' },
                { key: 'sepia', name: '📖 护眼', bg: 'bg-yellow-50', text: 'text-yellow-900', border: 'border-yellow-300' }
              ].map(theme => (
                <button
                  key={theme.key}
                  onClick={() => handleChange('theme', theme.key)}
                  className={`p-3 rounded-lg border-2 text-sm font-medium transition-all hover:scale-105 ${theme.bg} ${theme.text} ${theme.border} ${
                    settings.theme === theme.key ? 'ring-2 ring-blue-500 shadow-lg' : ''
                  }`}
                >
                  {theme.name}
                </button>
              ))}
            </div>
          </div>

          {/* 划词翻译 */}
          <div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Languages className="h-4 w-4" />
                <label className="text-sm font-medium">划词翻译</label>
              </div>
              <button
                type="button"
                onClick={() => updateTranslationSettings({ enabled: !translationSettings.enabled })}
                className={`relative inline-flex h-5 w-9 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  translationSettings.enabled ? 'bg-blue-600' : 'bg-gray-300'
                }`}
              >
                <span
                  className={`pointer-events-none inline-block h-4 w-4 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                    translationSettings.enabled ? 'translate-x-4' : 'translate-x-0'
                  }`}
                />
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              选中文本时显示翻译弹窗
            </p>
          </div>

          {/* 沉浸式翻译模式 */}
          <div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Languages className="h-4 w-4" />
                <label className="text-sm font-medium">沉浸式翻译</label>
              </div>
              <button
                type="button"
                onClick={() => {
                  const newValue = !translationSettings.immersiveMode;
                  console.log('🔧 点击沉浸式翻译开关');
                  console.log('🔧 当前值:', translationSettings.immersiveMode);
                  console.log('🔧 新值:', newValue);
                  console.log('🔧 调用updateTranslationSettings前的localStorage:', localStorage.getItem('translation_settings'));

                  updateTranslationSettings({ immersiveMode: newValue });

                  // 延迟检查保存结果
                  setTimeout(() => {
                    const afterSave = localStorage.getItem('translation_settings');
                    console.log('🔧 调用updateTranslationSettings后的localStorage:', afterSave);
                    if (afterSave) {
                      const parsed = JSON.parse(afterSave);
                      console.log('🔧 保存后的immersiveMode值:', parsed.immersiveMode);
                    }
                  }, 100);
                }}
                className={`relative inline-flex h-5 w-9 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  translationSettings.immersiveMode ? 'bg-blue-600' : 'bg-gray-300'
                }`}
                disabled={!translationSettings.enabled}
              >
                <span
                  className={`pointer-events-none inline-block h-4 w-4 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                    translationSettings.immersiveMode ? 'translate-x-4' : 'translate-x-0'
                  }`}
                />
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              左右对照显示原文和翻译
            </p>
          </div>

          {/* 字体 */}
          <div>
            <label className="block text-sm font-medium mb-2">字体选择</label>
            <select
              value={settings.fontFamily}
              onChange={(e) => handleChange('fontFamily', e.target.value)}
              className={`w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 ${getInputThemeClasses()}`}
            >
              <option value="system-ui">系统默认</option>
              <option value="serif">宋体 (衬线)</option>
              <option value="sans-serif">黑体 (无衬线)</option>
              <option value="monospace">等宽字体</option>
            </select>
          </div>

          {/* 页面宽度 */}
          <div>
            <label className="block text-sm font-medium mb-2">页面宽度</label>
            <div className="flex items-center space-x-2">
              <span className="text-sm">窄</span>
              <input
                type="range"
                min="600"
                max="1200"
                step="50"
                value={settings.maxWidth}
                onChange={(e) => handleChange('maxWidth', parseInt(e.target.value))}
                className="flex-1"
              />
              <span className="text-sm">宽</span>
              <span className="text-sm w-12">{settings.maxWidth}px</span>
            </div>
          </div>
        </div>

        {/* 调试区域 */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 mb-2">详细调试信息:</div>
          <div className="text-xs bg-gray-100 p-2 rounded mb-2 space-y-1">
            <div>Hook状态 - immersiveMode: {String(translationSettings.immersiveMode)}</div>
            <div>Hook状态 - enabled: {String(translationSettings.enabled)}</div>
            <div>Hook状态 - isLoading: {String(translationLoading)}</div>
            <div>localStorage原始: {localStorage.getItem('translation_settings')?.substring(0, 150)}...</div>
            <div>当前时间: {new Date().toLocaleTimeString()}</div>
          </div>
          <button
            onClick={() => {
              const stored = localStorage.getItem('translation_settings');
              console.log('🔧 当前localStorage内容:', stored);
              alert(`localStorage内容: ${stored}`);
            }}
            className="px-2 py-1 text-xs bg-yellow-500 text-white rounded mr-2"
          >
            检查存储
          </button>
          <button
            onClick={() => {
              localStorage.removeItem('translation_settings');
              console.log('🔧 已清除localStorage');
              alert('已清除localStorage，请刷新页面');
            }}
            className="px-2 py-1 text-xs bg-red-500 text-white rounded mr-2"
          >
            清除存储
          </button>
          <button
            onClick={() => {
              window.dispatchEvent(new Event('translation-settings-reload'));
              console.log('🔧 手动触发设置重新加载');
            }}
            className="px-2 py-1 text-xs bg-green-500 text-white rounded mr-2"
          >
            重新加载设置
          </button>
          <button
            onClick={() => {
              console.log('� [监听测试] 手动设置localStorage');
              localStorage.setItem('translation_settings', '{"test": true}');
              console.log('🔍 [监听测试] 设置完成');
            }}
            className="px-2 py-1 text-xs bg-orange-500 text-white rounded mr-2"
          >
            测试监听器
          </button>
          <button
            onClick={() => {
              console.log('�🔧 [详细测试] ===== 开始测试 =====');
              console.log('🔧 [详细测试] 当前translationSettings:', translationSettings);
              console.log('🔧 [详细测试] updateTranslationSettings函数:', updateTranslationSettings);
              console.log('🔧 [详细测试] updateTranslationSettings类型:', typeof updateTranslationSettings);
              console.log('🔧 [详细测试] updateTranslationSettings是否为函数:', typeof updateTranslationSettings === 'function');
              console.log('🔧 [详细测试] 测试前localStorage:', localStorage.getItem('translation_settings'));

              // 检查函数是否可用
              if (typeof updateTranslationSettings !== 'function') {
                console.error('❌ [详细测试] updateTranslationSettings 不是函数！');
                alert('错误：updateTranslationSettings 不是函数');
                return;
              }

              console.log('🔧 [详细测试] 即将调用updateTranslationSettings({ immersiveMode: false })');

              try {
                // 调用更新函数
                updateTranslationSettings({ immersiveMode: false });
                console.log('🔧 [详细测试] updateTranslationSettings调用完成，等待200ms后检查结果');
              } catch (error) {
                console.error('❌ [详细测试] updateTranslationSettings调用异常:', error);
                alert('错误：updateTranslationSettings调用异常 - ' + error.message);
                return;
              }

              // 检查结果
              setTimeout(() => {
                console.log('🔧 [详细测试] ===== 检查结果 =====');
                const stored = localStorage.getItem('translation_settings');
                console.log('🔧 [详细测试] 最终localStorage内容:', stored);

                if (stored) {
                  try {
                    const parsed = JSON.parse(stored);
                    const success = parsed.immersiveMode === false;
                    console.log('🔧 [详细测试] 解析结果:', parsed);
                    console.log('🔧 [详细测试] immersiveMode值:', parsed.immersiveMode);
                    console.log('🔧 [详细测试] 测试结果:', success ? '✅ 成功' : '❌ 失败');
                    alert(`[详细测试] 结果: ${success ? '成功' : '失败'}\nlocalStorage中immersiveMode: ${parsed.immersiveMode}`);
                  } catch (e) {
                    console.error('🔧 [详细测试] JSON解析失败:', e);
                    alert('[详细测试] localStorage解析失败');
                  }
                } else {
                  console.error('🔧 [详细测试] localStorage为空');
                  alert('[详细测试] localStorage为空');
                }
                console.log('🔧 [详细测试] ===== 测试结束 =====');
              }, 200);
            }}
            className="px-2 py-1 text-xs bg-purple-500 text-white rounded"
          >
            详细测试
          </button>
        </div>

        <div className="mt-6 flex justify-end space-x-2">
          <button
            onClick={() => onSettingsChange(DEFAULT_SETTINGS)}
            className={`px-4 py-2 text-sm border rounded hover:opacity-80 transition-opacity ${
              settings.theme === 'dark'
                ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            重置默认
          </button>
          <button
            onClick={() => {
              console.log('🔧 应用设置，当前翻译设置:', translationSettings);
              // 只是关闭设置面板，设置已经实时保存了
              onClose();
            }}
            className="px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            应用设置
          </button>
        </div>
      </div>
    </div>
  );
}

// Hook for managing reading settings
export function useReadingSettings(bookId?: string) {
  const [settings, setSettings] = useState<ReadingSettings>(DEFAULT_SETTINGS);

  useEffect(() => {
    // Load settings from localStorage
    try {
      const saved = localStorage.getItem('reading_settings');
      if (saved) {
        const parsed = JSON.parse(saved);
        setSettings({ ...DEFAULT_SETTINGS, ...parsed });
      }
    } catch (e) {
      console.warn('Failed to load reading settings:', e);
    }
  }, []);

  const updateSettings = (newSettings: ReadingSettings) => {
    setSettings(newSettings);
    try {
      localStorage.setItem('reading_settings', JSON.stringify(newSettings));
    } catch (e) {
      console.warn('Failed to save reading settings:', e);
    }
  };

  const getThemeClasses = () => {
    switch (settings.theme) {
      case 'dark':
        return 'bg-gray-900 text-gray-100';
      case 'sepia':
        return 'bg-yellow-50 text-yellow-900';
      default:
        return 'bg-white text-gray-900';
    }
  };

  const getSidebarThemeClasses = () => {
    switch (settings.theme) {
      case 'dark':
        return 'bg-gray-800 text-gray-100 border-gray-700';
      case 'sepia':
        return 'bg-yellow-100 text-yellow-900 border-yellow-200';
      default:
        return 'bg-gray-50 text-gray-900 border-gray-200';
    }
  };

  const getContentStyles = () => ({
    fontSize: `${settings.fontSize}px`,
    lineHeight: settings.lineHeight,
    fontFamily: settings.fontFamily,
    maxWidth: `${settings.maxWidth}px`
  });

  return {
    settings,
    updateSettings,
    getThemeClasses,
    getSidebarThemeClasses,
    getContentStyles
  };
}

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { openai, OPENAI_CONFIG } from '../lib/openai.js';
import { authenticateToken } from '../middleware/auth.js';

export async function translationRoutes(fastify: FastifyInstance) {

  // 测试翻译API（无需认证）
  fastify.post('/translate/test', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      console.log('🧪 测试翻译API请求体:', JSON.stringify(request.body, null, 2));
      const { text, sourceLanguage, targetLanguage, context } = (request.body as any);

      // 验证输入参数
      if (!text || typeof text !== 'string') {
        reply.code(400);
        return { success: false, error: '翻译文本不能为空' };
      }

      if (text.length > 10000) {
        reply.code(400);
        return { success: false, error: '翻译文本过长，最大支持10000字符' };
      }

      const source = sourceLanguage || 'auto';
      const target = targetLanguage || 'zh-CN';

      console.log(`🧪 测试翻译请求: "${text.substring(0, 50)}..." (${source} -> ${target})`);

      // 根据文本长度选择翻译策略
      let translationResult;
      if (text.length > 1000) {
        console.log('📄 使用分片翻译策略');
        translationResult = await callChunkedTranslation(text, source, target, context);
      } else {
        console.log('📝 使用单次翻译策略');
        translationResult = await callAIForTranslation(text, source, target, context);
      }

      if (translationResult.success) {
        return {
          success: true,
          data: {
            originalText: text,
            translatedText: translationResult.translation,
            sourceLanguage: translationResult.detectedLanguage || source,
            targetLanguage: target,
            context: context,
            timestamp: new Date().toISOString(),
            method: text.length > 1000 ? 'chunked' : 'single'
          }
        };
      } else {
        return {
          success: false,
          error: translationResult.error || '翻译失败，请稍后重试'
        };
      }

    } catch (error) {
      console.error('❌ 测试翻译API错误:', error);
      reply.code(500);
      return {
        success: false,
        error: '服务器内部错误，请稍后重试'
      };
    }
  });

  // 划词翻译API
  fastify.post('/translate', { preHandler: authenticateToken }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      console.log('🌐 翻译API请求体:', JSON.stringify(request.body, null, 2));
      const { text, sourceLanguage, targetLanguage, context } = (request.body as any);
      const userId = request.user?.userId;

      console.log('🌐 解析后的参数:', {
        text: text?.substring(0, 100) + '...',
        sourceLanguage,
        targetLanguage,
        context,
        userId
      });
      
      if (!userId) {
        reply.code(401);
        return { success: false, error: '用户认证失败' };
      }

      // 验证输入参数
      if (!text || typeof text !== 'string') {
        reply.code(400);
        return { success: false, error: '翻译文本不能为空' };
      }

      // 检查文本长度，如果过长则使用分片翻译
      if (text.length > 10000) {
        reply.code(400);
        return { success: false, error: '翻译文本过长，最大支持10000字符' };
      }

      const source = sourceLanguage || 'auto';
      const target = targetLanguage || 'zh-CN';

      console.log(`🌐 翻译请求: "${text.substring(0, 50)}..." (${source} -> ${target})`);

      // 根据文本长度选择翻译策略
      let translationResult;
      if (text.length > 1000) {
        // 使用分片翻译
        console.log('📄 使用分片翻译策略');
        translationResult = await callChunkedTranslation(text, source, target, context);
      } else {
        // 使用单次翻译
        console.log('📝 使用单次翻译策略');
        translationResult = await callAIForTranslation(text, source, target, context);
      }

      if (translationResult.success) {
        return {
          success: true,
          data: {
            originalText: text,
            translatedText: translationResult.translation,
            sourceLanguage: translationResult.detectedLanguage || source,
            targetLanguage: target,
            context: context,
            timestamp: new Date().toISOString()
          }
        };
      } else {
        return { 
          success: false, 
          error: translationResult.error || '翻译失败，请稍后重试' 
        };
      }

    } catch (error) {
      console.error('❌ 翻译API错误:', error);
      reply.code(500);
      return { 
        success: false, 
        error: '服务器内部错误，请稍后重试' 
      };
    }
  });

  // 批量翻译API（用于长文本）
  fastify.post('/translate/batch', { preHandler: authenticateToken }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      console.log('🌐 批量翻译API请求');
      const { text, sourceLanguage, targetLanguage, context } = (request.body as any);
      const userId = request.user?.userId;

      if (!userId) {
        reply.code(401);
        return { success: false, error: '用户认证失败' };
      }

      // 验证输入参数
      if (!text || typeof text !== 'string') {
        reply.code(400);
        return { success: false, error: '翻译文本不能为空' };
      }

      if (text.length > 50000) {
        reply.code(400);
        return { success: false, error: '翻译文本过长，最大支持50000字符' };
      }

      const source = sourceLanguage || 'auto';
      const target = targetLanguage || 'zh-CN';

      console.log(`🌐 批量翻译请求: ${text.length} 字符 (${source} -> ${target})`);

      // 强制使用分片翻译
      const translationResult = await callChunkedTranslation(text, source, target, context);

      if (translationResult.success) {
        return {
          success: true,
          data: {
            originalText: text,
            translatedText: translationResult.translation,
            sourceLanguage: translationResult.detectedLanguage || source,
            targetLanguage: target,
            context: context,
            timestamp: new Date().toISOString(),
            method: 'chunked'
          }
        };
      } else {
        return {
          success: false,
          error: translationResult.error || '批量翻译失败，请稍后重试'
        };
      }

    } catch (error) {
      console.error('❌ 批量翻译API错误:', error);
      reply.code(500);
      return {
        success: false,
        error: '服务器内部错误，请稍后重试'
      };
    }
  });

  // 获取支持的语言列表
  fastify.get('/translate/languages', { preHandler: authenticateToken }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user?.userId;
      
      if (!userId) {
        reply.code(401);
        return { success: false, error: '用户认证失败' };
      }

      const languages = [
        { code: 'auto', name: '自动检测', flag: '🌐' },
        { code: 'zh-CN', name: '简体中文', flag: '🇨🇳' },
        { code: 'zh-TW', name: '繁体中文', flag: '🇹🇼' },
        { code: 'en', name: 'English', flag: '🇺🇸' },
        { code: 'ja', name: '日本語', flag: '🇯🇵' },
        { code: 'ko', name: '한국어', flag: '🇰🇷' },
        { code: 'fr', name: 'Français', flag: '🇫🇷' },
        { code: 'de', name: 'Deutsch', flag: '🇩🇪' },
        { code: 'es', name: 'Español', flag: '🇪🇸' },
        { code: 'it', name: 'Italiano', flag: '🇮🇹' },
        { code: 'pt', name: 'Português', flag: '🇵🇹' },
        { code: 'ru', name: 'Русский', flag: '🇷🇺' },
        { code: 'ar', name: 'العربية', flag: '🇸🇦' },
        { code: 'hi', name: 'हिन्दी', flag: '🇮🇳' },
        { code: 'th', name: 'ไทย', flag: '🇹🇭' },
        { code: 'vi', name: 'Tiếng Việt', flag: '🇻🇳' }
      ];

      return {
        success: true,
        data: languages
      };

    } catch (error) {
      console.error('❌ 获取语言列表错误:', error);
      reply.code(500);
      return { 
        success: false, 
        error: '获取语言列表失败' 
      };
    }
  });
}

// AI翻译调用函数
async function callAIForTranslation(
  text: string, 
  sourceLanguage: string, 
  targetLanguage: string, 
  context?: string
): Promise<{ success: boolean; translation?: string; detectedLanguage?: string; error?: string }> {
  try {
    console.log('🤖 开始AI翻译...');

    // 构建翻译prompt
    const translationPrompt = buildTranslationPrompt(text, sourceLanguage, targetLanguage, context);

    const completion = await openai.chat.completions.create({
      model: OPENAI_CONFIG.defaultModel,
      messages: [
        {
          role: 'system',
          content: '你是一个专业的多语言翻译助手。你必须严格按照用户要求的JSON格式返回翻译结果。绝对不要返回其他格式的内容。你能准确识别语言并提供高质量的翻译，保持原文的语调、风格和含义。对于文学作品，你会特别注意保持文学性和美感。'
        },
        {
          role: 'user',
          content: translationPrompt
        }
      ],
      temperature: 0.3, // 较低的温度以确保翻译的一致性
      max_tokens: Math.min(2000, Math.max(500, Math.ceil(text.length * 1.5))) // 动态调整token数量
    });

    const response = completion.choices[0]?.message?.content?.trim();
    console.log('🤖 AI翻译响应:', response?.substring(0, 100) + '...');

    if (response) {
      // 清理可能的代码块标记
      let cleanedResponse = response.trim();

      // 移除可能的```json和```标记
      if (cleanedResponse.startsWith('```json')) {
        cleanedResponse = cleanedResponse.replace(/^```json\s*/, '');
      }
      if (cleanedResponse.endsWith('```')) {
        cleanedResponse = cleanedResponse.replace(/\s*```$/, '');
      }

      // 移除其他可能的标记
      cleanedResponse = cleanedResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');

      // 尝试解析JSON响应
      try {
        const parsed = JSON.parse(cleanedResponse);

        // 验证JSON结构
        if (parsed.translation && typeof parsed.translation === 'string') {
          return {
            success: true,
            translation: parsed.translation,
            detectedLanguage: parsed.detectedLanguage
          };
        } else {
          console.warn('AI返回的JSON格式不正确:', parsed);
          // 如果JSON格式不正确，尝试提取翻译内容
          return {
            success: true,
            translation: cleanedResponse
          };
        }
      } catch (parseError) {
        console.warn('AI返回非JSON格式，直接使用文本:', cleanedResponse);
        // 如果不是JSON格式，直接返回文本
        return {
          success: true,
          translation: cleanedResponse
        };
      }
    } else {
      return {
        success: false,
        error: 'AI未返回有效翻译'
      };
    }
  } catch (error) {
    console.error('❌ AI翻译调用失败:', error);

    // 提供降级方案
    return {
      success: false,
      error: 'AI翻译服务暂时不可用，请稍后重试'
    };
  }
}

// 构建翻译prompt
function buildTranslationPrompt(text: string, sourceLanguage: string, targetLanguage: string, context?: string): string {
  const languageNames: { [key: string]: string } = {
    'auto': '自动检测',
    'zh-CN': '简体中文',
    'zh-TW': '繁体中文',
    'en': '英语',
    'ja': '日语',
    'ko': '韩语',
    'fr': '法语',
    'de': '德语',
    'es': '西班牙语',
    'it': '意大利语',
    'pt': '葡萄牙语',
    'ru': '俄语',
    'ar': '阿拉伯语',
    'hi': '印地语',
    'th': '泰语',
    'vi': '越南语'
  };

  const sourceLangName = languageNames[sourceLanguage] || sourceLanguage;
  const targetLangName = languageNames[targetLanguage] || targetLanguage;

  let prompt = `请将以下文本翻译成${targetLangName}：

原文：${text}`;

  if (sourceLanguage !== 'auto') {
    prompt += `\n原文语言：${sourceLangName}`;
  }

  if (context) {
    prompt += `\n\n上下文信息：${context}`;
  }

  prompt += `

翻译要求：
1. 保持原文的语调和风格
2. 确保翻译准确、自然、流畅
3. 如果是文学作品，保持文学性和美感
4. 如果是专业术语，提供准确的专业翻译
5. 保持原文的格式和标点符号风格

重要：你必须严格按照以下JSON格式返回结果，绝对不要添加任何其他内容，包括代码块标记：

{
  "translation": "这里是翻译结果",
  "detectedLanguage": "检测到的语言代码"
}

严格要求：
- 只返回纯JSON格式，不要使用代码块标记
- 不要有任何解释、说明或额外文字
- translation字段必须包含完整的翻译结果
- 如果原文语言不是auto，detectedLanguage可以省略
- 确保JSON格式正确，可以被JavaScript解析
- 绝对不要在JSON前后添加任何标记或说明`;

  return prompt;
}

// 智能文本分片函数
function smartTextSplit(text: string, maxChunkSize: number = 800): string[] {
  const chunks: string[] = [];

  // 首先按段落分割
  const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim());

  let currentChunk = '';

  for (const paragraph of paragraphs) {
    const trimmedParagraph = paragraph.trim();

    // 如果当前段落本身就很长，需要进一步分割
    if (trimmedParagraph.length > maxChunkSize) {
      // 先保存当前chunk（如果有内容）
      if (currentChunk.trim()) {
        chunks.push(currentChunk.trim());
        currentChunk = '';
      }

      // 按句子分割长段落
      const sentences = trimmedParagraph.split(/([.!?。！？]\s+)/).filter(s => s.trim());
      let sentenceChunk = '';

      for (let i = 0; i < sentences.length; i += 2) {
        const sentence = sentences[i] + (sentences[i + 1] || '');

        if (sentenceChunk.length + sentence.length > maxChunkSize) {
          if (sentenceChunk.trim()) {
            chunks.push(sentenceChunk.trim());
          }
          sentenceChunk = sentence;
        } else {
          sentenceChunk += sentence;
        }
      }

      if (sentenceChunk.trim()) {
        currentChunk = sentenceChunk;
      }
    } else {
      // 检查添加这个段落是否会超出限制
      if (currentChunk.length + trimmedParagraph.length + 2 > maxChunkSize) {
        if (currentChunk.trim()) {
          chunks.push(currentChunk.trim());
        }
        currentChunk = trimmedParagraph;
      } else {
        currentChunk += (currentChunk ? '\n\n' : '') + trimmedParagraph;
      }
    }
  }

  // 添加最后一个chunk
  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }

  return chunks.filter(chunk => chunk.length > 0);
}

// 分片翻译函数
async function callChunkedTranslation(
  text: string,
  sourceLanguage: string,
  targetLanguage: string,
  context?: string
): Promise<{ success: boolean; translation?: string; detectedLanguage?: string; error?: string }> {
  try {
    console.log('🧩 开始分片翻译...');

    // 智能分片
    const chunks = smartTextSplit(text, 800);
    console.log(`📊 文本分为 ${chunks.length} 个片段`);

    const translatedChunks: string[] = [];
    let detectedLanguage: string | undefined;

    // 为了保持上下文连贯性，我们为每个片段提供前一个片段的部分内容作为上下文
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      console.log(`🔄 翻译片段 ${i + 1}/${chunks.length} (${chunk.length} 字符)`);

      // 构建上下文信息
      let chunkContext = context || '';
      if (i > 0 && translatedChunks[i - 1]) {
        // 添加前一个片段的翻译结果作为上下文（取最后200字符）
        const prevTranslation = translatedChunks[i - 1];
        const contextFromPrev = prevTranslation.length > 200
          ? '...' + prevTranslation.slice(-200)
          : prevTranslation;
        chunkContext += (chunkContext ? '\n\n' : '') +
          `前文翻译参考：${contextFromPrev}`;
      }

      // 翻译当前片段
      const result = await callAIForTranslation(chunk, sourceLanguage, targetLanguage, chunkContext);

      if (!result.success) {
        console.error(`❌ 片段 ${i + 1} 翻译失败:`, result.error);
        return {
          success: false,
          error: `翻译失败：第 ${i + 1} 个片段翻译出错 - ${result.error}`
        };
      }

      translatedChunks.push(result.translation || '');

      // 记录检测到的语言（使用第一个片段的结果）
      if (i === 0 && result.detectedLanguage) {
        detectedLanguage = result.detectedLanguage;
      }

      // 添加短暂延迟，避免API请求过于频繁（减少延迟以提高性能）
      if (i < chunks.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 50));
      }
    }

    // 合并翻译结果
    const finalTranslation = translatedChunks.join('\n\n');

    console.log('✅ 分片翻译完成');
    console.log(`📈 原文长度: ${text.length}, 译文长度: ${finalTranslation.length}`);

    return {
      success: true,
      translation: finalTranslation,
      detectedLanguage
    };

  } catch (error) {
    console.error('❌ 分片翻译失败:', error);
    return {
      success: false,
      error: '分片翻译服务出错，请稍后重试'
    };
  }
}

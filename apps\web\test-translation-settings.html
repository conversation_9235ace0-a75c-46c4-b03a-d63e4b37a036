<!DOCTYPE html>
<html>
<head>
    <title>测试翻译设置保存</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .button.danger { background: #dc3545; }
        .button.danger:hover { background: #c82333; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>翻译设置保存测试</h1>
    
    <div class="section">
        <h3>当前localStorage状态</h3>
        <button class="button" onclick="checkCurrentState()">检查当前状态</button>
        <button class="button danger" onclick="clearAllSettings()">清除所有设置</button>
        <pre id="currentState">点击"检查当前状态"查看</pre>
    </div>
    
    <div class="section">
        <h3>测试设置保存和加载</h3>
        <label>
            <input type="checkbox" id="immersiveModeTest"> 沉浸式翻译模式
        </label><br><br>
        <button class="button" onclick="saveTestSettings()">保存测试设置</button>
        <button class="button" onclick="loadTestSettings()">加载测试设置</button>
        <pre id="testResults">测试结果将显示在这里</pre>
    </div>
    
    <div class="section">
        <h3>模拟用户操作</h3>
        <p>模拟用户关闭沉浸式翻译并刷新页面的场景</p>
        <button class="button" onclick="simulateUserDisable()">1. 关闭沉浸式翻译</button>
        <button class="button" onclick="simulatePageRefresh()">2. 模拟页面刷新</button>
        <pre id="simulationResults">模拟结果将显示在这里</pre>
    </div>

    <script>
        const STORAGE_KEY = 'translation_settings';
        
        const DEFAULT_SETTINGS = {
            enabled: true,
            defaultSourceLanguage: 'auto',
            defaultTargetLanguage: 'zh-CN',
            autoDetectLanguage: true,
            showOriginalText: true,
            immersiveMode: false,
            immersiveSourceLanguage: 'en',
            immersiveTargetLanguage: 'zh-CN'
        };

        function checkCurrentState() {
            try {
                const stored = localStorage.getItem(STORAGE_KEY);
                const element = document.getElementById('currentState');
                
                if (stored) {
                    const parsed = JSON.parse(stored);
                    element.innerHTML = `<span class="success">找到设置:</span>\n${JSON.stringify(parsed, null, 2)}`;
                } else {
                    element.innerHTML = '<span class="warning">没有找到存储的设置</span>';
                }
                
                // 检查其他可能的存储键
                const otherKeys = ['reading_settings', 'ebook-platform-storage'];
                let otherData = '';
                otherKeys.forEach(key => {
                    const data = localStorage.getItem(key);
                    if (data) {
                        otherData += `\n\n${key}:\n${data}`;
                    }
                });
                
                if (otherData) {
                    element.innerHTML += `\n\n<span class="warning">其他相关存储:</span>${otherData}`;
                }
            } catch (error) {
                document.getElementById('currentState').innerHTML = `<span class="error">错误: ${error.message}</span>`;
            }
        }

        function clearAllSettings() {
            const keys = ['translation_settings', 'reading_settings', 'ebook-platform-storage'];
            keys.forEach(key => localStorage.removeItem(key));
            document.getElementById('currentState').innerHTML = '<span class="success">所有设置已清除</span>';
        }

        function saveTestSettings() {
            try {
                const immersiveMode = document.getElementById('immersiveModeTest').checked;
                const settings = {
                    ...DEFAULT_SETTINGS,
                    immersiveMode: immersiveMode
                };
                
                localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
                
                // 验证保存
                const saved = localStorage.getItem(STORAGE_KEY);
                const parsed = JSON.parse(saved);
                
                document.getElementById('testResults').innerHTML = 
                    `<span class="success">保存成功:</span>\nimmersiveMode: ${immersiveMode}\n\n验证结果:\n${JSON.stringify(parsed, null, 2)}`;
            } catch (error) {
                document.getElementById('testResults').innerHTML = `<span class="error">保存失败: ${error.message}</span>`;
            }
        }

        function loadTestSettings() {
            try {
                const stored = localStorage.getItem(STORAGE_KEY);
                if (stored) {
                    const parsedSettings = JSON.parse(stored);
                    
                    // 使用新的加载逻辑
                    const finalSettings = {
                        enabled: parsedSettings.enabled !== undefined ? parsedSettings.enabled : DEFAULT_SETTINGS.enabled,
                        defaultSourceLanguage: parsedSettings.defaultSourceLanguage || DEFAULT_SETTINGS.defaultSourceLanguage,
                        defaultTargetLanguage: parsedSettings.defaultTargetLanguage || DEFAULT_SETTINGS.defaultTargetLanguage,
                        autoDetectLanguage: parsedSettings.autoDetectLanguage !== undefined ? parsedSettings.autoDetectLanguage : DEFAULT_SETTINGS.autoDetectLanguage,
                        showOriginalText: parsedSettings.showOriginalText !== undefined ? parsedSettings.showOriginalText : DEFAULT_SETTINGS.showOriginalText,
                        immersiveMode: parsedSettings.immersiveMode !== undefined ? parsedSettings.immersiveMode : DEFAULT_SETTINGS.immersiveMode,
                        immersiveSourceLanguage: parsedSettings.immersiveSourceLanguage || DEFAULT_SETTINGS.immersiveSourceLanguage,
                        immersiveTargetLanguage: parsedSettings.immersiveTargetLanguage || DEFAULT_SETTINGS.immersiveTargetLanguage
                    };
                    
                    // 更新UI
                    document.getElementById('immersiveModeTest').checked = finalSettings.immersiveMode;
                    
                    document.getElementById('testResults').innerHTML = 
                        `<span class="success">加载成功:</span>\n原始存储: ${stored}\n\n解析结果: ${JSON.stringify(parsedSettings, null, 2)}\n\n最终设置: ${JSON.stringify(finalSettings, null, 2)}`;
                } else {
                    document.getElementById('testResults').innerHTML = '<span class="error">没有找到存储的设置</span>';
                }
            } catch (error) {
                document.getElementById('testResults').innerHTML = `<span class="error">加载失败: ${error.message}</span>`;
            }
        }

        function simulateUserDisable() {
            // 模拟用户关闭沉浸式翻译
            const settings = {
                ...DEFAULT_SETTINGS,
                immersiveMode: false
            };
            
            localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
            
            document.getElementById('simulationResults').innerHTML = 
                `<span class="success">步骤1完成:</span> 已保存 immersiveMode: false\n存储内容: ${JSON.stringify(settings, null, 2)}`;
        }

        function simulatePageRefresh() {
            // 模拟页面刷新时的加载过程
            try {
                const stored = localStorage.getItem(STORAGE_KEY);
                if (stored) {
                    const parsedSettings = JSON.parse(stored);
                    
                    // 使用新的加载逻辑
                    const finalSettings = {
                        enabled: parsedSettings.enabled !== undefined ? parsedSettings.enabled : DEFAULT_SETTINGS.enabled,
                        defaultSourceLanguage: parsedSettings.defaultSourceLanguage || DEFAULT_SETTINGS.defaultSourceLanguage,
                        defaultTargetLanguage: parsedSettings.defaultTargetLanguage || DEFAULT_SETTINGS.defaultTargetLanguage,
                        autoDetectLanguage: parsedSettings.autoDetectLanguage !== undefined ? parsedSettings.autoDetectLanguage : DEFAULT_SETTINGS.autoDetectLanguage,
                        showOriginalText: parsedSettings.showOriginalText !== undefined ? parsedSettings.showOriginalText : DEFAULT_SETTINGS.showOriginalText,
                        immersiveMode: parsedSettings.immersiveMode !== undefined ? parsedSettings.immersiveMode : DEFAULT_SETTINGS.immersiveMode,
                        immersiveSourceLanguage: parsedSettings.immersiveSourceLanguage || DEFAULT_SETTINGS.immersiveSourceLanguage,
                        immersiveTargetLanguage: parsedSettings.immersiveTargetLanguage || DEFAULT_SETTINGS.immersiveTargetLanguage
                    };
                    
                    const result = finalSettings.immersiveMode === false ? 
                        '<span class="success">✅ 测试通过: immersiveMode 正确保持为 false</span>' :
                        '<span class="error">❌ 测试失败: immersiveMode 被重置为 true</span>';
                    
                    document.getElementById('simulationResults').innerHTML += 
                        `\n\n<span class="success">步骤2完成:</span> 页面刷新加载\n${result}\n\n最终设置: ${JSON.stringify(finalSettings, null, 2)}`;
                } else {
                    document.getElementById('simulationResults').innerHTML += 
                        '\n\n<span class="error">步骤2失败: 没有找到存储的设置</span>';
                }
            } catch (error) {
                document.getElementById('simulationResults').innerHTML += 
                    `\n\n<span class="error">步骤2错误: ${error.message}</span>`;
            }
        }

        // 页面加载时检查状态
        window.onload = function() {
            checkCurrentState();
        };
    </script>
</body>
</html>

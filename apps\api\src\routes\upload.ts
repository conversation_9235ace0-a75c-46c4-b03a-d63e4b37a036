import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { MultipartFile } from '@fastify/multipart';
import { prisma } from '../lib/database.js';
import { saveFile, validateFile, getFileInfo } from '../lib/fileHandler.js';
import { caches, cacheKeys, cacheUtils } from '../lib/cache.js';
import { detectChapters, cleanText } from '@ebook-platform/utils';
import { analyzeChapters } from '../lib/openai.js';
import type { ApiResponse, Book } from '@ebook-platform/types';
import { authenticateToken } from '../middleware/auth.js';

// 文件上传路由
export async function uploadRoutes(fastify: FastifyInstance) {
  
  // 上传txt文件 - 需要认证
  fastify.post('/upload', { preHandler: authenticateToken }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // 获取上传的文件
      const data = await request.file();
      
      if (!data) {
        reply.code(400);
        return {
          success: false,
          error: '没有找到上传的文件'
        };
      }
      
      // 验证文件
      const validation = validateFile({
        filename: data.filename,
        mimetype: data.mimetype,
        file: data.file
      });
      
      if (!validation.valid) {
        reply.code(400);
        return {
          success: false,
          error: validation.error
        };
      }
      
      // 保存文件
      const savedFile = await saveFile({
        filename: data.filename,
        mimetype: data.mimetype,
        file: data.file
      });
      
      // 从认证中间件获取用户ID
      const userId = request.user!.userId;
      
      // 创建书籍记录
      const book = await prisma.book.create({
        data: {
          title: savedFile.originalName.replace(/\.(txt|TXT)$/, ''), // 移除扩展名作为标题
          filename: savedFile.filename,
          filePath: savedFile.filePath,
          fileSize: savedFile.size,
          userId: userId,
          status: 'parsing' // 设置为解析中状态
        }
      });
      
      // 异步处理文件解析（不阻塞响应）
      processFileAsync(book.id, savedFile.filePath).catch(error => {
        console.error(`处理文件失败 ${book.id}:`, error);
        // 更新书籍状态为错误
        prisma.book.update({
          where: { id: book.id },
          data: { status: 'error' }
        }).catch(console.error);
      });
      
      const response: ApiResponse<Book> = {
        success: true,
        data: {
          id: book.id,
          title: book.title,
          author: book.author ?? undefined,
          filename: book.filename,
          filePath: book.filePath,
          fileSize: book.fileSize,
          uploadedAt: book.uploadedAt,
          chapterCount: book.chapterCount,
          totalWords: book.totalWords,
          status: book.status as any
        },
        message: '文件上传成功，正在解析中...'
      };
      
      return response;
      
    } catch (error) {
      console.error('文件上传失败:', error);
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : '文件上传失败'
      };
    }
  });
  
  // 获取上传进度（预留接口）
  fastify.get('/upload/:bookId/progress', async (request: FastifyRequest<{
    Params: { bookId: string }
  }>, reply: FastifyReply) => {
    try {
      const { bookId } = request.params;
      
      const book = await prisma.book.findUnique({
        where: { id: bookId },
        select: {
          id: true,
          status: true,
          chapterCount: true,
          totalWords: true
        }
      });
      
      if (!book) {
        reply.code(404);
        return {
          success: false,
          error: '书籍不存在'
        };
      }
      
      return {
        success: true,
        data: {
          bookId: book.id,
          status: book.status,
          progress: book.status === 'ready' ? 100 : 
                   book.status === 'parsing' ? 50 : 
                   book.status === 'error' ? 0 : 0,
          chapterCount: book.chapterCount,
          totalWords: book.totalWords
        }
      };
      
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取进度失败'
      };
    }
  });
}

// 异步处理文件解析
async function processFileAsync(bookId: string, filePath: string): Promise<void> {
  try {
    console.log(`开始处理文件: ${bookId}`);
    
    // 读取文件内容，尝试多种编码
    const fs = await import('fs/promises');
    let content: string;

    try {
      // 首先尝试UTF-8
      content = await fs.readFile(filePath, 'utf-8');

      // 检查是否包含乱码字符
      if (content.includes('�') || content.includes('\uFFFD')) {
        console.log('⚠️ 检测到UTF-8乱码，尝试GBK编码...');

        // 尝试GBK编码（中文常用）
        const iconv = await import('iconv-lite');
        const buffer = await fs.readFile(filePath);
        content = iconv.decode(buffer, 'gbk');

        console.log('✅ 使用GBK编码成功读取文件');
      } else {
        console.log('✅ 使用UTF-8编码成功读取文件');
      }
    } catch (error) {
      console.error('❌ 文件编码读取失败:', error);
      throw new Error('文件编码不支持或文件损坏');
    }
    
    // 清理文本
    const cleanedContent = cleanText(content);
    
    // 先传统正则检测（兜底）
    console.log('📝 开始章节检测...');
    console.log('📄 文本内容预览:', cleanedContent.substring(0, 200) + '...');
    console.log('📏 文本总长度:', cleanedContent.length);
    const regexChapters = detectChapters(cleanedContent);
    console.log(`📊 正则检测到 ${regexChapters.length} 个章节`);

    // 标题清洗与章节规范化工具
    const MIN_GAP = Number.isFinite(Number(process.env.CHAPTER_MIN_GAP)) ? Math.max(0, parseInt(process.env.CHAPTER_MIN_GAP as string, 10)) : 30;
    const cleanupTitle = (s: string) => (s || '').replace(/\s+/g, ' ').replace(/[：:—\-\s]+$/,'').trim() || '无标题';
    const normalizeChapters = (list: Array<{ title: string; position: number }>) => {
      // 1) 清洗
      let arr = list.map(ch => ({ title: cleanupTitle(ch.title), position: Math.max(0, Math.floor(ch.position)) }));
      // 2) 排序
      arr.sort((a,b)=> a.position - b.position);
      // 3) 去重 + 最小间距
      const deduped: typeof arr = [];
      for (const ch of arr) {
        const last = deduped[deduped.length - 1];
        if (!last || Math.abs(ch.position - last.position) >= MIN_GAP) {
          deduped.push(ch);
        } else {
          if (ch.title.length > last.title.length) deduped[deduped.length - 1] = ch;
        }
      }
      // 4) 过滤非法
      return deduped.filter((ch, idx) => ch.position >= 0 && ch.position < cleanedContent.length && (idx === 0 || ch.position > deduped[idx-1].position));
    };

    // 尝试AI章节分析
    let chapters: Array<{ title: string; position: number }> = [];
    try {
      const aiResult = await analyzeChapters(cleanedContent);
      if (aiResult.success && aiResult.chapters.length > 0) {
        const aiChapters = normalizeChapters(
          aiResult.chapters.map(ch => ({
            title: ch.title || '无标题',
            position: Number.isFinite(ch.start_position) ? (ch.start_position as number) : 0
          }))
        );

        // 验证AI结果的合理性：检查章节数量和位置分布
        const regexCount = regexChapters.length;
        const aiCount = aiChapters.length;
        const textLength = cleanedContent.length;
        const minGap = Math.min(50, Math.floor(textLength / aiCount / 3)); // 动态计算最小间距

        const isAIReasonable = aiCount > 0 &&
          Math.abs(aiCount - regexCount) <= 1 && // 章节数相差不超过1
          aiChapters.every((ch, idx) => idx === 0 || ch.position > aiChapters[idx-1].position + minGap); // 动态章节间距

        console.log(`📊 验证参数: AI章节=${aiCount}, 正则章节=${regexCount}, 文本长度=${textLength}, 最小间距=${minGap}`);

        if (isAIReasonable) {
          chapters = aiChapters;
          console.log(`🤖 AI分析采用（验证通过），章节数: ${chapters.length}`);
          chapters.forEach((ch, idx) => {
            console.log(`  ${idx + 1}. "${ch.title}" (位置: ${ch.position})`);
          });
        } else {
          console.log(`⚠️ AI结果不合理（AI:${aiCount}章 vs 正则:${regexCount}章），使用正则结果`);
          console.log('AI章节详情:');
          aiChapters.forEach((ch, idx) => {
            console.log(`  ${idx + 1}. "${ch.title}" (位置: ${ch.position})`);
          });
          chapters = regexChapters.length > 0 ? normalizeChapters(regexChapters) : [{ title: '正文', position: 0 }];
          console.log('最终使用正则章节:');
          chapters.forEach((ch, idx) => {
            console.log(`  ${idx + 1}. "${ch.title}" (位置: ${ch.position})`);
          });
        }
      } else {
        // AI失败或文本过长时，回退到正则，确保全量文本覆盖
        const reason = aiResult.error || 'UNKNOWN_REASON';
        console.log(`⚠️ AI分析不可用（${reason}），使用正则结果，保证整本文本覆盖`);
        chapters = regexChapters.length > 0 ? normalizeChapters(regexChapters) : [{ title: '正文', position: 0 }];
      }
    } catch (e) {
      console.warn('⚠️ AI章节分析异常，回退到正则:', e instanceof Error ? e.message : e);
      chapters = regexChapters.length > 0 ? normalizeChapters(regexChapters) : [{ title: '正文', position: 0 }];
    }

    // 计算总字数
    const totalWords = cleanedContent.length;
    
    // 开始数据库事务
    await prisma.$transaction(async (tx) => {
      // 创建章节记录
      for (let i = 0; i < chapters.length; i++) {
        const chapter = chapters[i];
        const nextChapter = chapters[i + 1];
        
        const startPosition = chapter.position;
        const endPosition = nextChapter ? nextChapter.position : cleanedContent.length;
        const chapterContent = cleanedContent.slice(startPosition, endPosition);
        const wordCount = chapterContent.length;
        
        await tx.chapter.create({
          data: {
            bookId,
            title: chapter.title,
            orderIndex: i,
            startPosition,
            endPosition,
            wordCount
          }
        });
      }
      
      // 更新书籍信息
      await tx.book.update({
        where: { id: bookId },
        data: {
          chapterCount: chapters.length,
          totalWords,
          status: 'ready'
        }
      });
    });
    
    // 缓存文件内容
    cacheUtils.set(caches.books, cacheKeys.bookContent(bookId), cleanedContent, 3600);
    
    console.log(`文件处理完成: ${bookId}, ${chapters.length} 章节, ${totalWords} 字`);
    
  } catch (error) {
    console.error(`处理文件失败 ${bookId}:`, error);
    
    // 更新状态为错误
    await prisma.book.update({
      where: { id: bookId },
      data: { status: 'error' }
    }).catch(console.error);
    
    throw error;
  }
}

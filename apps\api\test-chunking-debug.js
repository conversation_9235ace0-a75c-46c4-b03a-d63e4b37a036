// 调试分片翻译的重复问题
const axios = require('axios');

const API_URL = 'http://localhost:3001/api';

// 测试文本，确保会被分片
const testText = `
第一段：这是第一段内容，用来测试分片翻译功能。这段内容应该足够长，以确保会被分成多个片段进行翻译。我们需要观察第一个片段是否会出现重复显示的问题。

第二段：这是第二段内容，继续测试分片翻译的连贯性。在分片翻译中，每个片段都应该独立翻译，然后合并成完整的结果。我们需要确保没有重复的内容出现。

第三段：这是第三段内容，用来进一步验证分片翻译的正确性。通过多个段落的测试，我们可以更好地理解分片策略的工作原理，并发现可能存在的问题。

第四段：这是最后一段内容，用来完成整个测试。通过这个完整的测试，我们应该能够识别并解决第一个片段重复显示的问题。
`.trim();

console.log('🔍 调试分片翻译重复问题...');
console.log(`📝 测试文本长度: ${testText.length} 字符`);
console.log('\n原始文本:');
console.log('=' .repeat(50));
console.log(testText);
console.log('=' .repeat(50));

async function debugChunkedTranslation() {
  try {
    console.log('\n🧪 开始翻译测试...');
    
    const response = await axios.post(`${API_URL}/translate/test`, {
      text: testText,
      sourceLanguage: 'zh-CN',
      targetLanguage: 'en'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 120000
    });

    if (response.data.success) {
      console.log('\n✅ 翻译成功!');
      console.log(`🔄 翻译方法: ${response.data.data.method}`);
      console.log(`📊 原文长度: ${response.data.data.originalText.length} 字符`);
      console.log(`📊 译文长度: ${response.data.data.translatedText.length} 字符`);
      
      console.log('\n翻译结果:');
      console.log('=' .repeat(50));
      console.log(response.data.data.translatedText);
      console.log('=' .repeat(50));
      
      // 分析是否有重复内容
      const translatedText = response.data.data.translatedText;
      const paragraphs = translatedText.split('\n\n').filter(p => p.trim());
      
      console.log('\n📋 分析翻译结果:');
      console.log(`分段数量: ${paragraphs.length}`);
      
      paragraphs.forEach((paragraph, index) => {
        console.log(`\n段落 ${index + 1}:`);
        console.log(`长度: ${paragraph.length} 字符`);
        console.log(`内容: ${paragraph.substring(0, 100)}${paragraph.length > 100 ? '...' : ''}`);
      });
      
      // 检查是否有重复的段落
      const duplicates = [];
      for (let i = 0; i < paragraphs.length; i++) {
        for (let j = i + 1; j < paragraphs.length; j++) {
          if (paragraphs[i].trim() === paragraphs[j].trim()) {
            duplicates.push({ index1: i, index2: j, content: paragraphs[i] });
          }
        }
      }
      
      if (duplicates.length > 0) {
        console.log('\n⚠️ 发现重复内容:');
        duplicates.forEach(dup => {
          console.log(`段落 ${dup.index1 + 1} 和段落 ${dup.index2 + 1} 重复:`);
          console.log(dup.content.substring(0, 100) + '...');
        });
      } else {
        console.log('\n✅ 未发现重复内容');
      }
      
    } else {
      console.error('❌ 翻译失败:', response.data.error);
    }

  } catch (error) {
    console.error('❌ 测试失败:');
    console.error('状态码:', error.response?.status);
    console.error('错误信息:', error.response?.data);
    console.error('错误详情:', error.message);
  }
}

debugChunkedTranslation().catch(console.error);

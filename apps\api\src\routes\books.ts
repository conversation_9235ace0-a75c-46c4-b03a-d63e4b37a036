import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { prisma } from '../lib/database.js';
import { caches, cacheKeys, cacheUtils } from '../lib/cache.js';
import { authenticateToken } from '../middleware/auth.js';
import type { Book, ApiResponse } from '@ebook-platform/types';

// 书籍相关路由
export async function booksRoutes(fastify: FastifyInstance) {

  // 获取用户的所有书籍 - 需要认证
  fastify.get('/books', { preHandler: authenticateToken }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // 从认证中间件获取用户ID
      const userId = request.user?.userId;

      if (!userId) {
        reply.code(401);
        return { success: false, error: '用户认证失败' };
      }

      const books = await prisma.book.findMany({
        where: { userId },
        orderBy: { uploadedAt: 'desc' },
        include: {
          _count: {
            select: {
              chapters: true,
              bookmarks: true
            }
          }
        }
      });

      const response: ApiResponse<Book[]> = {
        success: true,
        data: books.map(book => ({
          id: book.id,
          title: book.title,
          author: book.author ?? undefined,
          filename: book.filename,
          filePath: book.filePath,
          fileSize: book.fileSize,
          uploadedAt: book.uploadedAt,
          chapterCount: book.chapterCount,
          totalWords: book.totalWords,
          status: book.status as any
        }))
      };

      return response;
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取书籍列表失败'
      };
    }
  });

  // 获取单本书籍详情 - 需要认证
  fastify.get('/books/:bookId', { preHandler: authenticateToken }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { bookId } = (request.params as any);
      const userId = request.user?.userId;

      if (!userId) {
        reply.code(401);
        return { success: false, error: '用户认证失败' };
      }

      // 先尝试从缓存获取
      const cacheKey = cacheKeys.book(bookId);
      let book = cacheUtils.get(caches.books, cacheKey);

      if (!book) {
        book = await prisma.book.findUnique({
          where: {
            id: bookId,
            userId: userId  // 确保只能访问自己的书籍
          },
          include: {
            chapters: {
              orderBy: { orderIndex: 'asc' },
              select: {
                id: true,
                title: true,
                orderIndex: true,
                wordCount: true
              }
            },
            _count: {
              select: {
                bookmarks: true
              }
            }
          }
        });

        if (!book) {
          reply.code(404);
          return {
            success: false,
            error: '书籍不存在'
          };
        }

        // 缓存书籍信息
        cacheUtils.set(caches.books, cacheKey, book, 3600); // 1小时
      }

      const response: ApiResponse<any> = {
        success: true,
        data: book
      };

      return response;
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取书籍详情失败'
      };
    }
  });

  // 删除书籍
  fastify.delete('/books/:bookId', async (request: FastifyRequest<{
    Params: { bookId: string }
  }>, reply: FastifyReply) => {
    try {
      const { bookId } = request.params;

      // 检查书籍是否存在
      const book = await prisma.book.findUnique({
        where: { id: bookId }
      });

      if (!book) {
        reply.code(404);
        return {
          success: false,
          error: '书籍不存在'
        };
      }

      // 删除数据库记录（级联删除相关数据）
      await prisma.book.delete({
        where: { id: bookId }
      });

      // 清除相关缓存
      cacheUtils.del(caches.books, cacheKeys.book(bookId));
      cacheUtils.del(caches.books, cacheKeys.bookContent(bookId));
      cacheUtils.delByPrefix(caches.chapters, `chapter_content:${bookId}:`);

      // TODO: 删除文件系统中的文件
      // await deleteFile(book.filePath);

      return {
        success: true,
        message: '书籍删除成功'
      };
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除书籍失败'
      };
    }
  });

  // 更新书籍信息
  fastify.patch('/books/:bookId', async (request: FastifyRequest<{
    Params: { bookId: string };
    Body: { title?: string; author?: string }
  }>, reply: FastifyReply) => {
    try {
      const { bookId } = request.params;
      const { title, author } = request.body;

      const updatedBook = await prisma.book.update({
        where: { id: bookId },
        data: {
          ...(title && { title }),
          ...(author && { author })
        }
      });

      // 清除缓存
      cacheUtils.del(caches.books, cacheKeys.book(bookId));

      return {
        success: true,
        data: updatedBook,
        message: '书籍信息更新成功'
      };
    } catch (error) {
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新书籍信息失败'
      };
    }
  });

  // 获取阅读进度 - 需要认证
  fastify.get('/books/:bookId/progress', { preHandler: authenticateToken }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { bookId } = (request.params as any);
      const userId = request.user?.userId;

      if (!userId) {
        reply.code(401);
        return { success: false, error: '用户认证失败' };
      }

      // 确保书籍存在且属于当前用户
      const book = await prisma.book.findFirst({
        where: {
          id: bookId,
          userId: userId
        },
        select: { id: true }
      });
      if (!book) {
        reply.code(404);
        return { success: false, error: '书籍不存在或无权限访问' };
      }

      const progress = await prisma.readingProgress.findFirst({ where: { userId, bookId } });
      if (!progress) {
        return { success: true, data: { currentChapter: 0, currentPosition: 0, percentage: 0, lastReadAt: new Date().toISOString() } };
      }
      return { success: true, data: progress };
    } catch (e) {
      reply.code(500);
      return { success: false, error: e instanceof Error ? e.message : '获取阅读进度失败' };
    }
  });

  // 更新阅读进度 - 需要认证
  fastify.put('/books/:bookId/progress', { preHandler: authenticateToken }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { bookId } = (request.params as any);
      const { currentChapter = 0, currentPosition = 0, percentage = 0 } = (request.body as any) || {};
      const userId = request.user?.userId;

      if (!userId) {
        reply.code(401);
        return { success: false, error: '用户认证失败' };
      }

      // 确保书籍存在且属于当前用户
      const book = await prisma.book.findFirst({
        where: {
          id: bookId,
          userId: userId
        },
        select: { id: true }
      });
      if (!book) {
        reply.code(404);
        return { success: false, error: '书籍不存在或无权限访问' };
      }

      const existing = await prisma.readingProgress.findFirst({ where: { userId, bookId } });
      let saved;
      if (!existing) {
        saved = await prisma.readingProgress.create({
          data: { userId, bookId, currentChapter, currentPosition, percentage }
        });
      } else {
        saved = await prisma.readingProgress.update({
          where: { id: existing.id },
          data: { currentChapter, currentPosition, percentage, lastReadAt: new Date() }
        });
      }
      return { success: true, data: saved };
    } catch (e) {
      reply.code(500);
      return { success: false, error: e instanceof Error ? e.message : '更新阅读进度失败' };
    }
  });

  // 获取书签列表 - 需要认证
  fastify.get('/books/:bookId/bookmarks', { preHandler: authenticateToken }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { bookId } = (request.params as any);
      const userId = request.user?.userId;

      if (!userId) {
        reply.code(401);
        return { success: false, error: '用户认证失败' };
      }

      const bookmarks = await prisma.bookmark.findMany({
        where: { userId, bookId },
        include: { chapter: { select: { title: true, orderIndex: true } } },
        orderBy: { createdAt: 'desc' }
      });

      return { success: true, data: bookmarks };
    } catch (e) {
      reply.code(500);
      return { success: false, error: e instanceof Error ? e.message : '获取书签失败' };
    }
  });

  // 添加书签 - 需要认证
  fastify.post('/books/:bookId/bookmarks', { preHandler: authenticateToken }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { bookId } = (request.params as any);
      const { chapterId, position, note } = (request.body as any);
      const userId = request.user?.userId;

      if (!userId) {
        reply.code(401);
        return { success: false, error: '用户认证失败' };
      }

      const bookmark = await prisma.bookmark.create({
        data: {
          userId,
          bookId,
          chapterId,
          position,
          note: note || '',
          type: 'manual'
        },
        include: { chapter: { select: { title: true, orderIndex: true } } }
      });

      return { success: true, data: bookmark };
    } catch (e) {
      reply.code(500);
      return { success: false, error: e instanceof Error ? e.message : '添加书签失败' };
    }
  });

  // 删除书签 - 需要认证
  fastify.delete('/bookmarks/:bookmarkId', { preHandler: authenticateToken }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { bookmarkId } = (request.params as any);
      const userId = request.user?.userId;

      if (!userId) {
        reply.code(401);
        return { success: false, error: '用户认证失败' };
      }

      // 确保只能删除自己的书签
      await prisma.bookmark.deleteMany({
        where: {
          id: bookmarkId,
          userId
        }
      });

      return { success: true, message: '书签删除成功' };
    } catch (e) {
      reply.code(500);
      return { success: false, error: e instanceof Error ? e.message : '删除书签失败' };
    }
  });

}

// 简单测试Node.js调用PowerShell
const { spawn } = require('child_process');
const path = require('path');

async function testPowerShellCall() {
  return new Promise((resolve) => {
    console.log('🧪 测试Node.js调用PowerShell...');
    
    const testText = "第一章 测试章节\n\n这是第一章的内容。\n\n第二章 另一个章节\n\n这是第二章的内容。";
    const scriptPath = path.join(process.cwd(), 'scripts', 'openai-bridge.ps1');
    
    console.log('📝 测试文本:', testText);
    console.log('📄 脚本路径:', scriptPath);
    
    // 启动PowerShell进程
    const ps = spawn('powershell.exe', [
      '-ExecutionPolicy', 'Bypass',
      '-File', scriptPath,
      '-Text', testText
    ], {
      stdio: ['pipe', 'pipe', 'pipe'],
      encoding: 'utf8'
    });

    let stdout = '';
    let stderr = '';

    ps.stdout.on('data', (data) => {
      stdout += data.toString();
      console.log('📤 stdout:', data.toString());
    });

    ps.stderr.on('data', (data) => {
      stderr += data.toString();
      console.log('❌ stderr:', data.toString());
    });

    ps.on('close', (code) => {
      console.log(`🏁 进程结束，退出码: ${code}`);
      console.log('📊 完整stdout:', stdout);
      console.log('📊 完整stderr:', stderr);
      
      if (code === 0) {
        console.log('✅ PowerShell调用成功!');
        
        // 尝试解析JSON
        try {
          const jsonMatch = stdout.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            const parsed = JSON.parse(jsonMatch[0]);
            console.log('📋 解析结果:', parsed);
            resolve({ success: true, chapters: parsed.chapters || [] });
          } else {
            console.log('⚠️ 未找到JSON响应');
            resolve({ success: false, error: '未找到JSON响应' });
          }
        } catch (parseError) {
          console.error('❌ JSON解析失败:', parseError);
          resolve({ success: false, error: 'JSON解析失败' });
        }
      } else {
        console.log('❌ PowerShell调用失败');
        resolve({ success: false, error: `PowerShell执行失败: ${stderr}` });
      }
    });

    ps.on('error', (error) => {
      console.error('❌ PowerShell进程启动失败:', error);
      resolve({ success: false, error: `PowerShell启动失败: ${error.message}` });
    });
  });
}

// 运行测试
testPowerShellCall()
  .then(result => {
    console.log('🎯 最终结果:', result);
    process.exit(result.success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 测试失败:', error);
    process.exit(1);
  });

import { promises as fs } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import mime from 'mime-types';
import { formatFileSize } from '@ebook-platform/utils';

// 文件存储配置
export const FILE_CONFIG = {
  uploadDir: process.env.UPLOAD_DIR || './data/uploads',
  maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '52428800'), // 50MB
  allowedMimeTypes: ['text/plain', 'application/octet-stream'],
  allowedExtensions: ['.txt']
};

// 确保上传目录存在
export const ensureUploadDir = async (): Promise<void> => {
  try {
    await fs.access(FILE_CONFIG.uploadDir);
  } catch {
    await fs.mkdir(FILE_CONFIG.uploadDir, { recursive: true });
    console.log(`Created upload directory: ${FILE_CONFIG.uploadDir}`);
  }
};

// 生成唯一文件名
export const generateFileName = (originalName: string): string => {
  const ext = path.extname(originalName);
  const baseName = path.basename(originalName, ext);
  const timestamp = Date.now();
  const uuid = uuidv4().slice(0, 8);
  
  return `${baseName}_${timestamp}_${uuid}${ext}`;
};

// 获取文件完整路径
export const getFilePath = (filename: string): string => {
  return path.join(FILE_CONFIG.uploadDir, filename);
};

// 验证文件
export const validateFile = (file: {
  filename: string;
  mimetype: string;
  file: NodeJS.ReadableStream;
}): { valid: boolean; error?: string } => {
  // 检查文件扩展名
  const ext = path.extname(file.filename).toLowerCase();
  if (!FILE_CONFIG.allowedExtensions.includes(ext)) {
    return {
      valid: false,
      error: `不支持的文件类型。仅支持: ${FILE_CONFIG.allowedExtensions.join(', ')}`
    };
  }
  
  // 检查MIME类型
  const mimeType = mime.lookup(file.filename);
  if (mimeType && !FILE_CONFIG.allowedMimeTypes.includes(mimeType)) {
    return {
      valid: false,
      error: `不支持的MIME类型: ${mimeType}`
    };
  }
  
  return { valid: true };
};

// 保存文件
export const saveFile = async (
  file: {
    filename: string;
    mimetype: string;
    file: NodeJS.ReadableStream;
  }
): Promise<{
  filename: string;
  originalName: string;
  filePath: string;
  size: number;
}> => {
  // 验证文件
  const validation = validateFile(file);
  if (!validation.valid) {
    throw new Error(validation.error);
  }
  
  // 确保上传目录存在
  await ensureUploadDir();
  
  // 生成新文件名
  const newFilename = generateFileName(file.filename);
  const filePath = getFilePath(newFilename);
  
  // 保存文件并计算大小
  let size = 0;
  const writeStream = await fs.open(filePath, 'w');
  
  try {
    const chunks: Buffer[] = [];
    
    for await (const chunk of file.file) {
      const buffer = Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk);
      chunks.push(buffer);
      size += buffer.length;
      
      // 检查文件大小限制
      if (size > FILE_CONFIG.maxFileSize) {
        await writeStream.close();
        await fs.unlink(filePath); // 删除部分上传的文件
        throw new Error(`文件大小超过限制 (${formatFileSize(FILE_CONFIG.maxFileSize)})`);
      }
    }
    
    // 写入文件
    const fileBuffer = Buffer.concat(chunks);
    await writeStream.writeFile(fileBuffer);
    
    return {
      filename: newFilename,
      originalName: file.filename,
      filePath,
      size
    };
  } finally {
    await writeStream.close();
  }
};

// 读取文件内容
export const readFileContent = async (filePath: string): Promise<string> => {
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    return content;
  } catch (error) {
    throw new Error(`读取文件失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

// 删除文件
export const deleteFile = async (filePath: string): Promise<void> => {
  try {
    await fs.unlink(filePath);
  } catch (error) {
    console.error(`删除文件失败: ${filePath}`, error);
    // 不抛出错误，因为文件可能已经不存在
  }
};

// 检查文件是否存在
export const fileExists = async (filePath: string): Promise<boolean> => {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
};

// 获取文件信息
export const getFileInfo = async (filePath: string): Promise<{
  size: number;
  createdAt: Date;
  modifiedAt: Date;
}> => {
  try {
    const stats = await fs.stat(filePath);
    return {
      size: stats.size,
      createdAt: stats.birthtime,
      modifiedAt: stats.mtime
    };
  } catch (error) {
    throw new Error(`获取文件信息失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

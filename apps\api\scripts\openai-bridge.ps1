# PowerShell script for calling OpenAI API
param(
    [Parameter(Mandatory=$true)]
    [string]$Text,

    [Parameter(Mandatory=$false)]
    [string]$ApiKey = "",

    [Parameter(Mandatory=$false)]
    [string]$Model = "gpt-4o-mini",

    [Parameter(Mandatory=$false)]
    [string]$ApiUrl = ""
)

# --- Load config from environment variables (preferred) ---
$envApiKey = [Environment]::GetEnvironmentVariable('OPENAI_API_KEY')
$envBaseUrl = [Environment]::GetEnvironmentVariable('OPENAI_BASE_URL')
$envModel  = [Environment]::GetEnvironmentVariable('OPENAI_MODEL')

if (![string]::IsNullOrWhiteSpace($envApiKey)) { $ApiKey = $envApiKey }
if (![string]::IsNullOrWhiteSpace($envModel))  { $Model  = $envModel }

if (![string]::IsNullOrWhiteSpace($envBaseUrl)) {
    $ApiUrl = ("{0}/v1/chat/completions" -f $envBaseUrl.TrimEnd('/'))
}

# Fallback default if still empty (official endpoint)
if ([string]::IsNullOrWhiteSpace($ApiUrl)) {
    $ApiUrl = "https://api.openai.com/v1/chat/completions"
}

try {
    # Build request prompt
    $prompt = "You are a professional text chapter analysis assistant. Please analyze the following text and identify chapter division points.

Requirements:
1. Identify all possible chapter titles and division points
2. Return results in JSON format
3. Include chapter title, start position, chapter type and other information
4. If there are no explicit chapters, please make reasonable divisions based on content semantics

Text content:
$Text

Please return results strictly in the following JSON format:
{
  `"chapters`": [
    {
      `"title`": `"Chapter Title`",
      `"start_position`": 0,
      `"confidence`": 0.95,
      `"type`": `"explicit`"
    }
  ]
}"

    $requestBody = @{
        model = $Model
        messages = @(
            @{
                role = "user"
                content = $prompt
            }
        )
        max_tokens = 1200
        temperature = 0.1
    } | ConvertTo-Json -Depth 10

    # Send request
    $headers = @{
        "Authorization" = "Bearer $ApiKey"
        "Content-Type" = "application/json"
    }

    Write-Host "Starting OpenAI API call..." -ForegroundColor Yellow
    Write-Host "API URL: $ApiUrl" -ForegroundColor Cyan
    Write-Host "Model: $Model" -ForegroundColor Cyan
    Write-Host "Text length: $($Text.Length) characters" -ForegroundColor Cyan

    $response = Invoke-WebRequest -Uri $ApiUrl -Method POST -Headers $headers -Body $requestBody -ContentType "application/json"

    if ($response.StatusCode -eq 200) {
        $responseData = $response.Content | ConvertFrom-Json
        $aiResponse = $responseData.choices[0].message.content

        Write-Host "API call successful!" -ForegroundColor Green
        Write-Host "Status code: $($response.StatusCode)" -ForegroundColor Green

        # Output result to stdout for Node.js to read
        Write-Output $aiResponse
    } else {
        Write-Host "API call failed: $($response.StatusCode)" -ForegroundColor Red
        Write-Error "API call failed: $($response.StatusCode) $($response.StatusDescription)"
        exit 1
    }

} catch {
    Write-Host "Script execution failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Error $_.Exception.Message
    exit 1
}

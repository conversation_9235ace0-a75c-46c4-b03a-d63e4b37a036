/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
    '../../packages/ui/src/**/*.{ts,tsx}'
  ],
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px'
      }
    },
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))'
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))'
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))'
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))'
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))'
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))'
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))'
        },
        // 阅读主题色彩
        reading: {
          light: {
            bg: '#ffffff',
            text: '#1a1a1a',
            secondary: '#f5f5f5'
          },
          dark: {
            bg: '#1a1a1a',
            text: '#e5e5e5',
            secondary: '#2a2a2a'
          },
          sepia: {
            bg: '#f4f1ea',
            text: '#5c4b37',
            secondary: '#ede4d3'
          }
        }
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)'
      },
      fontFamily: {
        sans: ['var(--font-sans)', 'system-ui', 'sans-serif'],
        serif: ['var(--font-serif)', 'Georgia', 'serif'],
        mono: ['var(--font-mono)', 'Consolas', 'monospace'],
        // 阅读字体
        reading: ['var(--font-reading)', 'Georgia', 'serif']
      },
      fontSize: {
        // 阅读字体大小
        'reading-xs': ['0.75rem', { lineHeight: '1.6' }],
        'reading-sm': ['0.875rem', { lineHeight: '1.6' }],
        'reading-base': ['1rem', { lineHeight: '1.7' }],
        'reading-lg': ['1.125rem', { lineHeight: '1.7' }],
        'reading-xl': ['1.25rem', { lineHeight: '1.8' }],
        'reading-2xl': ['1.5rem', { lineHeight: '1.8' }],
        'reading-3xl': ['1.875rem', { lineHeight: '1.8' }]
      },
      spacing: {
        // 阅读间距
        'reading': '1.7rem'
      },
      keyframes: {
        'accordion-down': {
          from: { height: 0 },
          to: { height: 'var(--radix-accordion-content-height)' }
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: 0 }
        },
        'fade-in': {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' }
        },
        'slide-in': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' }
        }
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'fade-in': 'fade-in 0.3s ease-out',
        'slide-in': 'slide-in 0.3s ease-out'
      },
      maxWidth: {
        'reading': '65ch' // 阅读最佳行长度
      }
    }
  },
  plugins: [
    require('@tailwindcss/typography'),
    // 自定义插件：阅读模式
    function({ addUtilities }) {
      const newUtilities = {
        '.reading-container': {
          maxWidth: '65ch',
          margin: '0 auto',
          padding: '0 1rem'
        },
        '.reading-text': {
          lineHeight: '1.7',
          fontSize: '1.125rem',
          color: 'var(--reading-text)',
          fontFamily: 'var(--font-reading)'
        },
        '.reading-paragraph': {
          marginBottom: '1.5rem',
          textIndent: '2em'
        }
      };
      addUtilities(newUtilities);
    }
  ]
};

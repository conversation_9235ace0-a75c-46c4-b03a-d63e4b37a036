'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { Bookmark, BookOpen, Calendar, MessageSquare, Trash2, ChevronRight } from 'lucide-react';
import { api } from '@/lib/api';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import type { Book } from '@ebook-platform/types';

interface BookmarkWithBook {
  id: string;
  position: number;
  note?: string;
  createdAt: string;
  chapter: {
    title: string;
    orderIndex: number;
  };
  book: {
    id: string;
    title: string;
  };
}

export default function BookmarksPage() {
  const router = useRouter();
  const [bookmarks, setBookmarks] = useState<BookmarkWithBook[]>([]);
  const [books, setBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedBookId, setSelectedBookId] = useState<string>('all');

  // 获取用户的所有书籍和书签
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // 获取书籍列表
        const booksResponse = await api.books.list();
        if (booksResponse.success) {
          const userBooks = booksResponse.data || [];
          setBooks(userBooks);
          
          // 获取所有书籍的书签
          const allBookmarks: BookmarkWithBook[] = [];
          for (const book of userBooks) {
            try {
              const bookmarksResponse = await api.bookmarks.list(book.id);
              if (bookmarksResponse.success && bookmarksResponse.data) {
                const bookBookmarks = bookmarksResponse.data.map((bookmark: any) => ({
                  ...bookmark,
                  book: {
                    id: book.id,
                    title: book.title
                  }
                }));
                allBookmarks.push(...bookBookmarks);
              }
            } catch (err) {
              console.error(`获取书籍 ${book.title} 的书签失败:`, err);
            }
          }
          
          // 按创建时间排序
          allBookmarks.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
          setBookmarks(allBookmarks);
        } else {
          setError(booksResponse.message || '获取数据失败');
        }
      } catch (err) {
        setError('网络错误，请稍后重试');
        console.error('获取数据失败:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // 删除书签
  const handleDeleteBookmark = async (bookmarkId: string) => {
    if (!confirm('确定要删除这个书签吗？')) {
      return;
    }

    try {
      const response = await api.bookmarks.delete(bookmarkId);
      if (response.success) {
        setBookmarks(prev => prev.filter(b => b.id !== bookmarkId));
      } else {
        alert('删除书签失败：' + (response.message || '未知错误'));
      }
    } catch (err) {
      alert('删除书签失败，请稍后重试');
      console.error('删除书签失败:', err);
    }
  };

  // 跳转到书签位置
  const handleJumpToBookmark = (bookmark: BookmarkWithBook) => {
    router.push(`/read/${bookmark.book.id}?chapter=${bookmark.chapter.orderIndex}`);
  };

  // 过滤书签
  const filteredBookmarks = selectedBookId === 'all' 
    ? bookmarks 
    : bookmarks.filter(b => b.book.id === selectedBookId);

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <ProtectedRoute>
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* 页面头部 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Bookmark className="h-8 w-8 text-indigo-600" />
                我的书签
              </h1>
              <p className="text-gray-600 mt-2">管理您的阅读书签</p>
            </div>
          </div>

          {/* 书籍筛选 */}
          {books.length > 1 && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                筛选书籍
              </label>
              <select
                value={selectedBookId}
                onChange={(e) => setSelectedBookId(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              >
                <option value="all">所有书籍 ({bookmarks.length})</option>
                {books.map(book => {
                  const bookBookmarkCount = bookmarks.filter(b => b.book.id === book.id).length;
                  return (
                    <option key={book.id} value={book.id}>
                      {book.title} ({bookBookmarkCount})
                    </option>
                  );
                })}
              </select>
            </div>
          )}
        </motion.div>

        {/* 内容区域 */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
              <p className="text-gray-600">加载中...</p>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
              <p className="text-red-600 mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                重试
              </button>
            </div>
          </div>
        ) : filteredBookmarks.length === 0 ? (
          <div className="text-center py-12">
            <Bookmark className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              {selectedBookId === 'all' ? '还没有任何书签' : '这本书还没有书签'}
            </h3>
            <p className="text-gray-600 mb-6">
              在阅读时添加书签来标记重要内容
            </p>
            <button
              onClick={() => router.push('/books')}
              className="inline-flex items-center px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              <BookOpen className="h-5 w-5 mr-2" />
              去阅读
            </button>
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="space-y-4"
          >
            {filteredBookmarks.map((bookmark, index) => (
              <motion.div
                key={bookmark.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="bg-white rounded-lg shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div 
                    className="flex-1 cursor-pointer"
                    onClick={() => handleJumpToBookmark(bookmark)}
                  >
                    {/* 书籍和章节信息 */}
                    <div className="flex items-center gap-2 mb-3">
                      <BookOpen className="h-4 w-4 text-indigo-600" />
                      <span className="font-medium text-gray-900">
                        {bookmark.book.title}
                      </span>
                      <ChevronRight className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-600">
                        {bookmark.chapter.title}
                      </span>
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        位置 {bookmark.position}
                      </span>
                    </div>

                    {/* 书签备注 */}
                    {bookmark.note && (
                      <div className="mb-3">
                        <p className="text-gray-700 bg-gray-50 rounded-lg px-3 py-2 border-l-4 border-indigo-200">
                          <MessageSquare className="h-4 w-4 inline mr-2 text-indigo-600" />
                          {bookmark.note}
                        </p>
                      </div>
                    )}

                    {/* 创建时间 */}
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <Calendar className="h-4 w-4" />
                      {formatDate(bookmark.createdAt)}
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center gap-2 ml-4">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleJumpToBookmark(bookmark);
                      }}
                      className="px-3 py-1.5 text-sm bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
                    >
                      跳转
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteBookmark(bookmark.id);
                      }}
                      className="p-1.5 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                      title="删除书签"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* 统计信息 */}
        {!loading && !error && bookmarks.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mt-8 text-center text-sm text-gray-500"
          >
            共 {bookmarks.length} 个书签
            {selectedBookId !== 'all' && filteredBookmarks.length !== bookmarks.length && (
              <span>，当前显示 {filteredBookmarks.length} 个</span>
            )}
          </motion.div>
        )}
      </div>
    </ProtectedRoute>
  );
}

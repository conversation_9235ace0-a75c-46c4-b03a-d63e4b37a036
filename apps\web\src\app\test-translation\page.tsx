'use client';

import React, { useState } from 'react';
import { ImmersiveTranslation } from '@/components/reading/ImmersiveTranslation';
import { useTranslationSettings } from '@/hooks/useTranslationSettings';

const testContent = `Hello, this is a test content for immersive translation.

This paragraph contains multiple sentences. We want to see how the translation system handles different types of content. The system should be able to translate this content from English to Chinese.

Another paragraph here. This one is shorter but still meaningful for testing purposes.

Final paragraph with some technical terms like "artificial intelligence" and "machine learning" to test specialized vocabulary translation.`;

export default function TestTranslationPage() {
  const { settings, updateSettings } = useTranslationSettings();
  const [sourceLanguage, setSourceLanguage] = useState('en');
  const [targetLanguage, setTargetLanguage] = useState('zh-CN');

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">沉浸式翻译测试页面</h1>
          
          {/* 控制面板 */}
          <div className="bg-white rounded-lg shadow p-4 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  翻译功能状态
                </label>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => updateSettings({ enabled: !settings.enabled })}
                    className={`px-3 py-1 rounded text-sm ${
                      settings.enabled 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {settings.enabled ? '已启用' : '已禁用'}
                  </button>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  沉浸式模式
                </label>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => updateSettings({ immersiveMode: !settings.immersiveMode })}
                    className={`px-3 py-1 rounded text-sm ${
                      settings.immersiveMode 
                        ? 'bg-blue-100 text-blue-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}
                    disabled={!settings.enabled}
                  >
                    {settings.immersiveMode ? '已开启' : '已关闭'}
                  </button>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  当前设置
                </label>
                <div className="text-xs text-gray-600">
                  <div>enabled: {String(settings.enabled)}</div>
                  <div>immersiveMode: {String(settings.immersiveMode)}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 翻译内容区域 */}
        <div className="bg-white rounded-lg shadow overflow-hidden" style={{ height: '600px' }}>
          {settings.enabled && settings.immersiveMode ? (
            <ImmersiveTranslation
              content={testContent}
              sourceLanguage={sourceLanguage}
              targetLanguage={targetLanguage}
              onLanguageChange={(source, target) => {
                setSourceLanguage(source);
                setTargetLanguage(target);
              }}
              className="h-full"
            />
          ) : (
            <div className="p-8 text-center">
              <div className="mb-4">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  沉浸式翻译未启用
                </h3>
                <p className="text-gray-600">
                  请先启用翻译功能和沉浸式模式来查看效果
                </p>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4 text-left max-w-2xl mx-auto">
                <h4 className="font-medium text-gray-900 mb-2">测试内容预览：</h4>
                <div className="text-sm text-gray-700 whitespace-pre-wrap">
                  {testContent}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

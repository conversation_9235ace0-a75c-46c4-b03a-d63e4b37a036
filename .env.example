# 数据库配置
DATABASE_URL="file:./data/database.sqlite"

# API服务器配置
API_PORT=3001
API_HOST=localhost

# 前端配置
NEXT_PUBLIC_API_URL=http://localhost:3001

# 文件存储配置
UPLOAD_DIR=./data/uploads
MAX_FILE_SIZE=50MB

# OpenAI配置（可选，用于AI功能）
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo

# 翻译服务配置（可选）
TRANSLATION_SERVICE=openai
# TRANSLATION_SERVICE=google
# GOOGLE_TRANSLATE_API_KEY=your_google_api_key

# 开发环境配置
NODE_ENV=development
LOG_LEVEL=info

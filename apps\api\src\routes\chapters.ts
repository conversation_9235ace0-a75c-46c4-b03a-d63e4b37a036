import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { prisma } from '../lib/database.js';
import { caches, cacheKeys, cacheUtils } from '../lib/cache.js';
import { analyzeChapters, openai, OPENAI_CONFIG } from '../lib/openai.js';

export async function chaptersRoutes(fastify: FastifyInstance) {
  // 列出章节
  fastify.get('/books/:bookId/chapters', async (request: FastifyRequest<{ Params: { bookId: string } }>, reply: FastifyReply) => {
    const { bookId } = request.params;
    try {
      const book = await prisma.book.findUnique({
        where: { id: bookId },
        select: { id: true }
      });
      if (!book) {
        reply.code(404);
        return { success: false, error: '书籍不存在' };
      }

      const chapters = await prisma.chapter.findMany({
        where: { bookId },
        orderBy: { orderIndex: 'asc' },
        select: { id: true, title: true, orderIndex: true, wordCount: true }
      });

      return { success: true, data: chapters };
    } catch (e) {
      reply.code(500);
      return { success: false, error: e instanceof Error ? e.message : '获取章节列表失败' };
    }
  });

  // 获取单章元信息
  fastify.get('/books/:bookId/chapters/:index', async (request: FastifyRequest<{ Params: { bookId: string; index: string } }>, reply: FastifyReply) => {
    const { bookId, index } = request.params;
    try {
      const orderIndex = parseInt(index, 10);
      const chapter = await prisma.chapter.findFirst({
        where: { bookId, orderIndex },
        select: { id: true, title: true, orderIndex: true, startPosition: true, endPosition: true, wordCount: true }
      });
      if (!chapter) {
        reply.code(404);
        return { success: false, error: '章节不存在' };
      }
      return { success: true, data: chapter };
    } catch (e) {
      reply.code(500);
      return { success: false, error: e instanceof Error ? e.message : '获取章节失败' };
    }
  });

  // 获取章节内容
  fastify.get('/books/:bookId/chapters/:index/content', async (request: FastifyRequest<{ Params: { bookId: string; index: string } }>, reply: FastifyReply) => {
    const { bookId, index } = request.params;
    try {
      const orderIndex = parseInt(index, 10);

      const chapterCache = cacheUtils.get(caches.chapters, cacheKeys.chapterContent(bookId, orderIndex));
      if (chapterCache) {
        return { success: true, data: chapterCache };
      }

      const chapter = await prisma.chapter.findFirst({
        where: { bookId, orderIndex },
        select: { startPosition: true, endPosition: true }
      });
      if (!chapter) {
        reply.code(404);
        return { success: false, error: '章节不存在' };
      }

      // 优先从缓存取全文
      const fullText = cacheUtils.get(caches.books, cacheKeys.bookContent(bookId));
      if (!fullText || typeof fullText !== 'string') {
        // 如果没有缓存，则从文件读取
        const book = await prisma.book.findUnique({ where: { id: bookId }, select: { filePath: true } });
        if (!book) {
          reply.code(404);
          return { success: false, error: '书籍不存在' };
        }
        const fs = await import('fs/promises');
        const content = await fs.readFile(book.filePath, 'utf8');
        const start = Math.max(0, chapter.startPosition);
        const end = Math.max(start, chapter.endPosition);
        const segment = content.slice(start, end);
        cacheUtils.set(caches.chapters, cacheKeys.chapterContent(bookId, orderIndex), segment, 3600);
        return { success: true, data: segment };
      }

      const start = Math.max(0, chapter.startPosition);
      const end = Math.max(start, chapter.endPosition);
      const segment = (fullText as string).slice(start, end);
      cacheUtils.set(caches.chapters, cacheKeys.chapterContent(bookId, orderIndex), segment, 3600);
      return { success: true, data: segment };
    } catch (e) {
      reply.code(500);
      return { success: false, error: e instanceof Error ? e.message : '获取章节内容失败' };
    }
  });

  // AI章节总结
  fastify.post('/books/:bookId/chapters/:index/summary', async (request: FastifyRequest<{
    Params: { bookId: string; index: string }
  }>, reply: FastifyReply) => {
    const { bookId, index } = request.params;
    try {
      const orderIndex = parseInt(index, 10);

      // 获取章节信息和内容
      const chapter = await prisma.chapter.findFirst({
        where: { bookId, orderIndex },
        select: { id: true, title: true, startPosition: true, endPosition: true }
      });
      if (!chapter) {
        reply.code(404);
        return { success: false, error: '章节不存在' };
      }

      // 获取章节内容
      let content = '';
      const fullText = cacheUtils.get(caches.books, cacheKeys.bookContent(bookId));
      if (fullText && typeof fullText === 'string') {
        content = fullText.slice(chapter.startPosition, chapter.endPosition);
      } else {
        // 从文件读取
        const book = await prisma.book.findUnique({ where: { id: bookId }, select: { filePath: true } });
        if (!book) {
          reply.code(404);
          return { success: false, error: '书籍不存在' };
        }
        const fs = await import('fs/promises');
        let fileContent: string;

        try {
          // 首先尝试UTF-8
          fileContent = await fs.readFile(book.filePath, 'utf8');

          // 检查是否包含乱码字符
          if (fileContent.includes('�') || fileContent.includes('\uFFFD')) {
            console.log('⚠️ 检测到UTF-8乱码，尝试GBK编码...');

            // 尝试GBK编码
            const iconv = await import('iconv-lite');
            const buffer = await fs.readFile(book.filePath);
            fileContent = iconv.decode(buffer, 'gbk');
          }
        } catch (error) {
          console.error('❌ 文件编码读取失败:', error);
          throw new Error('文件编码不支持或文件损坏');
        }

        content = fileContent.slice(chapter.startPosition, chapter.endPosition);
      }

      if (!content.trim()) {
        return { success: false, error: '章节内容为空' };
      }

      // 检查内容编码
      console.log('章节内容预览:', content.substring(0, 100));
      console.log('内容长度:', content.length);

      // 调用AI进行总结
      const summaryPrompt = `请为以下章节内容生成一个结构化的总结，严格按照以下格式输出：

**${chapter.title}总结**

**主要情节和关键信息**
[概括本章的主要故事情节和核心信息]

**重要人物和事件**
[列出本章出现的重要人物及其关键行为/事件]

**转折点**
[识别本章中的重要转折点或关键发展]

章节内容：
${content.slice(0, 3000)} ${content.length > 3000 ? '...(内容过长，已截取前3000字符)' : ''}

请严格按照上述格式提供总结，每个部分都要有具体内容：`;

      // 这里使用现有的OpenAI接口进行总结
      // 由于现有的analyzeChapters是用于章节分析的，我们需要一个通用的AI调用
      // 暂时使用简化的实现
      const aiResponse = await callOpenAIForSummary(summaryPrompt);

      if (aiResponse.success) {
        return {
          success: true,
          data: {
            chapterTitle: chapter.title,
            summary: aiResponse.summary,
            wordCount: content.length,
            generatedAt: new Date().toISOString()
          }
        };
      } else {
        return { success: false, error: aiResponse.error || 'AI总结失败' };
      }

    } catch (e) {
      reply.code(500);
      return { success: false, error: e instanceof Error ? e.message : '生成章节总结失败' };
    }
  });
}

// AI总结调用函数
async function callOpenAIForSummary(prompt: string): Promise<{ success: boolean; summary?: string; error?: string }> {
  try {
    console.log('🤖 开始AI章节总结...');

    // 使用与章节分析相同的SDK方法
    const completion = await openai.chat.completions.create({
      model: OPENAI_CONFIG.defaultModel,
      messages: [
        {
          role: 'system',
          content: '你是一个专业的文本总结助手。请严格按照用户指定的格式输出结构化的章节总结，包含主要情节、重要人物事件和转折点三个部分。使用Markdown格式，保持内容简洁准确。'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 500,
      temperature: 0.7
    });

    const summary = completion.choices[0]?.message?.content?.trim();
    console.log('🤖 AI总结响应:', summary?.substring(0, 100) + '...');

    if (summary) {
      return {
        success: true,
        summary: summary
      };
    } else {
      return {
        success: false,
        error: 'AI未返回有效总结'
      };
    }
  } catch (error) {
    console.error('❌ AI总结调用失败:', error);

    // 如果AI调用失败，返回一个基础的总结
    const fallbackSummary = `本章节内容丰富，包含了重要的情节发展和人物互动。由于AI服务暂时不可用，建议您稍后重试以获取更详细的智能总结。`;

    return {
      success: true,
      summary: fallbackSummary
    };
  }
}


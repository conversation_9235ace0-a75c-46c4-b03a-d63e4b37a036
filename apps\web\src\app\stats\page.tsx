'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { TrendingUp, BookOpen, Clock, Calendar, Target, Award, ChevronRight } from 'lucide-react';
import { api } from '@/lib/api';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import type { Book, ReadingProgress } from '@ebook-platform/types';

interface BookWithProgress extends Book {
  progress?: ReadingProgress;
}

export default function StatsPage() {
  const router = useRouter();
  const [books, setBooks] = useState<BookWithProgress[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取书籍和进度数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // 获取书籍列表
        const booksResponse = await api.books.list();
        if (booksResponse.success) {
          const userBooks = booksResponse.data || [];
          
          // 获取每本书的阅读进度
          const booksWithProgress = await Promise.all(
            userBooks.map(async (book) => {
              try {
                const progressResponse = await api.progress.get(book.id);
                return {
                  ...book,
                  progress: progressResponse.success ? progressResponse.data : undefined
                };
              } catch (err) {
                console.error(`获取书籍 ${book.title} 的进度失败:`, err);
                return book;
              }
            })
          );
          
          setBooks(booksWithProgress);
        } else {
          setError(booksResponse.message || '获取数据失败');
        }
      } catch (err) {
        setError('网络错误，请稍后重试');
        console.error('获取数据失败:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // 计算统计数据
  const stats = React.useMemo(() => {
    const totalBooks = books.length;
    const booksWithProgress = books.filter(book => book.progress);
    const completedBooks = books.filter(book => book.progress && book.progress.percentage >= 100);
    const inProgressBooks = books.filter(book => book.progress && book.progress.percentage > 0 && book.progress.percentage < 100);
    
    const totalWords = books.reduce((sum, book) => sum + (book.totalWords || 0), 0);
    const readWords = books.reduce((sum, book) => {
      if (book.progress && book.totalWords) {
        return sum + Math.round((book.totalWords * book.progress.percentage) / 100);
      }
      return sum;
    }, 0);

    const averageProgress = booksWithProgress.length > 0 
      ? booksWithProgress.reduce((sum, book) => sum + (book.progress?.percentage || 0), 0) / booksWithProgress.length
      : 0;

    return {
      totalBooks,
      completedBooks: completedBooks.length,
      inProgressBooks: inProgressBooks.length,
      totalWords,
      readWords,
      averageProgress: Math.round(averageProgress)
    };
  }, [books]);

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 10000) {
      return `${(num / 10000).toFixed(1)}万`;
    }
    return num.toLocaleString();
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <ProtectedRoute>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* 页面头部 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <TrendingUp className="h-8 w-8 text-indigo-600" />
                阅读统计
              </h1>
              <p className="text-gray-600 mt-2">查看您的阅读进度和成就</p>
            </div>
          </div>
        </motion.div>

        {/* 内容区域 */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
              <p className="text-gray-600">加载中...</p>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
              <p className="text-red-600 mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                重试
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            {/* 统计卡片 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
            >
              <div className="bg-white rounded-lg shadow-md border p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">总书籍数</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalBooks}</p>
                  </div>
                  <BookOpen className="h-8 w-8 text-blue-600" />
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md border p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">已完成</p>
                    <p className="text-2xl font-bold text-green-600">{stats.completedBooks}</p>
                  </div>
                  <Award className="h-8 w-8 text-green-600" />
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md border p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">阅读中</p>
                    <p className="text-2xl font-bold text-orange-600">{stats.inProgressBooks}</p>
                  </div>
                  <Clock className="h-8 w-8 text-orange-600" />
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md border p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">平均进度</p>
                    <p className="text-2xl font-bold text-indigo-600">{stats.averageProgress}%</p>
                  </div>
                  <Target className="h-8 w-8 text-indigo-600" />
                </div>
              </div>
            </motion.div>

            {/* 阅读量统计 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-lg shadow-md border p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">阅读量统计</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">总字数</span>
                    <span className="font-medium">{formatNumber(stats.totalWords)} 字</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-gray-400 h-2 rounded-full" style={{ width: '100%' }} />
                  </div>
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">已阅读</span>
                    <span className="font-medium text-indigo-600">{formatNumber(stats.readWords)} 字</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-indigo-600 h-2 rounded-full transition-all duration-500" 
                      style={{ width: `${stats.totalWords > 0 ? (stats.readWords / stats.totalWords) * 100 : 0}%` }} 
                    />
                  </div>
                </div>
              </div>
            </motion.div>

            {/* 书籍进度列表 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-lg shadow-md border"
            >
              <div className="p-6 border-b">
                <h3 className="text-lg font-semibold text-gray-900">书籍阅读进度</h3>
              </div>
              <div className="divide-y divide-gray-100">
                {books.length === 0 ? (
                  <div className="p-8 text-center">
                    <BookOpen className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                    <p className="text-gray-500">还没有上传任何书籍</p>
                  </div>
                ) : (
                  books.map((book, index) => (
                    <motion.div
                      key={book.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className="p-6 hover:bg-gray-50 transition-colors cursor-pointer"
                      onClick={() => router.push(`/read/${book.id}`)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 mb-1">{book.title}</h4>
                          {book.author && (
                            <p className="text-sm text-gray-600 mb-2">{book.author}</p>
                          )}
                          
                          {book.progress ? (
                            <div className="space-y-2">
                              <div className="flex items-center justify-between text-sm">
                                <span className="text-gray-600">
                                  第 {book.progress.currentChapter + 1} 章
                                </span>
                                <span className="font-medium text-indigo-600">
                                  {book.progress.percentage}%
                                </span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div 
                                  className="bg-indigo-600 h-2 rounded-full transition-all duration-500" 
                                  style={{ width: `${book.progress.percentage}%` }} 
                                />
                              </div>
                              <div className="flex items-center gap-4 text-xs text-gray-500">
                                <span className="flex items-center gap-1">
                                  <Calendar className="h-3 w-3" />
                                  {formatDate(book.progress.lastReadAt.toString())}
                                </span>
                                {book.totalWords && (
                                  <span>
                                    {formatNumber(Math.round((book.totalWords * book.progress.percentage) / 100))} / {formatNumber(book.totalWords)} 字
                                  </span>
                                )}
                              </div>
                            </div>
                          ) : (
                            <div className="text-sm text-gray-500">
                              <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                                <div className="bg-gray-300 h-2 rounded-full" style={{ width: '0%' }} />
                              </div>
                              <span>尚未开始阅读</span>
                            </div>
                          )}
                        </div>
                        
                        <ChevronRight className="h-5 w-5 text-gray-400 ml-4" />
                      </div>
                    </motion.div>
                  ))
                )}
              </div>
            </motion.div>
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
}

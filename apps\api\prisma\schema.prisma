// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:../../../data/database.sqlite"
}

model User {
  id           String   @id @default(cuid())
  username     String   @unique
  email        String   @unique
  passwordHash String
  displayName  String?
  avatar       String?
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // 关联关系
  books            Book[]
  readingProgress  ReadingProgress[]
  bookmarks        Bookmark[]

  @@map("users")
}

model Book {
  id           String    @id @default(cuid())
  title        String
  author       String?
  filename     String
  filePath     String
  fileSize     Int
  uploadedAt   DateTime  @default(now())
  chapterCount Int       @default(0)
  totalWords   Int       @default(0)
  status       String    @default("uploading") // uploading, parsing, ready, error
  
  // 关联用户
  userId       String
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // 关联关系
  chapters         Chapter[]
  readingProgress  ReadingProgress[]
  bookmarks        Bookmark[]
  summaries        ChapterSummary[]

  @@map("books")
}

model Chapter {
  id            String @id @default(cuid())
  title         String
  orderIndex    Int
  startPosition Int
  endPosition   Int
  wordCount     Int    @default(0)
  
  // 关联书籍
  bookId        String
  book          Book   @relation(fields: [bookId], references: [id], onDelete: Cascade)
  
  // 关联关系
  bookmarks     Bookmark[]
  summaries     ChapterSummary[]

  @@unique([bookId, orderIndex])
  @@map("chapters")
}

model ReadingProgress {
  id              String   @id @default(cuid())
  currentChapter  Int      @default(0)
  currentPosition Int      @default(0)
  percentage      Float    @default(0)
  lastReadAt      DateTime @default(now())
  
  // 关联用户和书籍
  userId          String
  bookId          String
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  book            Book     @relation(fields: [bookId], references: [id], onDelete: Cascade)

  @@unique([userId, bookId])
  @@map("reading_progress")
}

model Bookmark {
  id        String   @id @default(cuid())
  position  Int
  note      String?
  type      String   @default("manual") // manual, auto_highlight, note
  createdAt DateTime @default(now())
  
  // 关联用户、书籍和章节
  userId    String
  bookId    String
  chapterId String
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  book      Book    @relation(fields: [bookId], references: [id], onDelete: Cascade)
  chapter   Chapter @relation(fields: [chapterId], references: [id], onDelete: Cascade)

  @@map("bookmarks")
}

model ChapterSummary {
  id          String   @id @default(cuid())
  summary     String
  keyPoints   String   // JSON array
  characters  String   // JSON array
  generatedAt DateTime @default(now())
  
  // 关联书籍和章节
  bookId      String
  chapterId   String
  book        Book    @relation(fields: [bookId], references: [id], onDelete: Cascade)
  chapter     Chapter @relation(fields: [chapterId], references: [id], onDelete: Cascade)

  @@unique([chapterId])
  @@map("chapter_summaries")
}

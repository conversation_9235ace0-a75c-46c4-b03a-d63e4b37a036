{"name": "@ebook-platform/api", "version": "0.1.0", "private": true, "description": "电子书平台后端API服务", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts", "clean": "rm -rf dist", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@ebook-platform/types": "file:../../packages/types", "@ebook-platform/utils": "file:../../packages/utils", "@fastify/cors": "^8.4.0", "@fastify/env": "^4.2.0", "@fastify/multipart": "^8.0.0", "@fastify/static": "^6.12.0", "@prisma/client": "^5.6.0", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "fastify": "^4.24.0", "https-proxy-agent": "^7.0.6", "iconv-lite": "^0.6.3", "jsonwebtoken": "^9.0.2", "mime-types": "^2.1.35", "node-cache": "^5.1.2", "openai": "^5.12.2", "prisma": "^5.6.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/mime-types": "^2.1.4", "@types/node": "^20.8.0", "@types/uuid": "^9.0.7", "tsx": "^4.0.0", "typescript": "^5.2.0"}}
// 用户相关类型
export interface User {
  id: string;
  username: string;
  email?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 书籍相关类型
export interface Book {
  id: string;
  title: string;
  author?: string;
  filename: string;
  filePath: string;
  fileSize: number;
  uploadedAt: Date;
  chapterCount: number;
  totalWords: number;
  status: BookStatus;
}

export enum BookStatus {
  UPLOADING = 'uploading',
  PARSING = 'parsing',
  READY = 'ready',
  ERROR = 'error'
}

// 章节相关类型
export interface Chapter {
  id: string;
  bookId: string;
  title: string;
  orderIndex: number;
  startPosition: number;
  endPosition: number;
  wordCount: number;
  content?: string;
}

// 阅读进度类型
export interface ReadingProgress {
  id: string;
  userId: string;
  bookId: string;
  currentChapter: number;
  currentPosition: number;
  percentage: number;
  lastReadAt: Date;
}

// 书签类型
export interface Bookmark {
  id: string;
  userId: string;
  bookId: string;
  chapterId: string;
  position: number;
  note?: string;
  createdAt: Date;
  type: BookmarkType;
}

export enum BookmarkType {
  MANUAL = 'manual',
  AUTO_HIGHLIGHT = 'auto_highlight',
  NOTE = 'note'
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 翻译相关类型
export interface TranslationRequest {
  text: string;
  sourceLanguage?: string;
  targetLanguage: string;
  context?: string;
}

export interface TranslationResponse {
  originalText: string;
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  context?: string;
  timestamp: string;
}

export interface Language {
  code: string;
  name: string;
  flag: string;
}

export interface TranslationSettings {
  enabled: boolean;
  defaultSourceLanguage: string;
  defaultTargetLanguage: string;
  autoDetectLanguage: boolean;
  showOriginalText: boolean;
  immersiveMode: boolean;
  immersiveSourceLanguage: string;
  immersiveTargetLanguage: string;
}

// 文件上传类型
export interface UploadResponse {
  bookId: string;
  filename: string;
  size: number;
  status: BookStatus;
}

// 阅读设置类型
export interface ReadingSettings {
  fontSize: number;
  fontFamily: string;
  lineHeight: number;
  theme: 'light' | 'dark' | 'sepia';
  backgroundColor: string;
  textColor: string;
  maxWidth: number;
}

// 翻译相关类型
export interface TranslationRequest {
  text: string;
  targetLanguage: string;
  sourceLanguage?: string;
}

export interface TranslationResponse {
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  confidence?: number;
}

// AI功能类型
export interface ChapterSummary {
  chapterId: string;
  summary: string;
  keyPoints: string[];
  characters: string[];
  generatedAt: Date;
}

export interface CharacterRelation {
  name: string;
  relations: Array<{
    target: string;
    relationship: string;
    strength: number;
  }>;
}

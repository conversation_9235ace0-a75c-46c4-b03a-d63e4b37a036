<!DOCTYPE html>
<html>
<head>
    <title>调试 localStorage</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>翻译设置 localStorage 调试工具</h1>
    
    <div class="section">
        <h3>当前存储的翻译设置</h3>
        <button class="button" onclick="showCurrentSettings()">查看当前设置</button>
        <button class="button" onclick="clearSettings()">清除设置</button>
        <pre id="currentSettings">点击"查看当前设置"按钮</pre>
    </div>
    
    <div class="section">
        <h3>测试设置保存</h3>
        <button class="button" onclick="testSaveSettings()">测试保存设置</button>
        <button class="button" onclick="testLoadSettings()">测试加载设置</button>
        <pre id="testResults">点击测试按钮</pre>
    </div>
    
    <div class="section">
        <h3>手动设置</h3>
        <label>
            <input type="checkbox" id="immersiveMode"> 沉浸式翻译模式
        </label><br><br>
        <button class="button" onclick="saveManualSettings()">保存手动设置</button>
        <pre id="manualResults">手动设试结果将显示在这里</pre>
    </div>

    <script>
        const STORAGE_KEY = 'translation_settings';
        
        const DEFAULT_SETTINGS = {
            enabled: true,
            defaultSourceLanguage: 'auto',
            defaultTargetLanguage: 'zh-CN',
            autoDetectLanguage: true,
            showOriginalText: true,
            immersiveMode: false,
            immersiveSourceLanguage: 'en',
            immersiveTargetLanguage: 'zh-CN'
        };

        function showCurrentSettings() {
            try {
                const stored = localStorage.getItem(STORAGE_KEY);
                const element = document.getElementById('currentSettings');
                
                if (stored) {
                    const parsed = JSON.parse(stored);
                    element.innerHTML = `<span class="success">找到设置:</span>\n${JSON.stringify(parsed, null, 2)}`;
                } else {
                    element.innerHTML = '<span class="error">没有找到存储的设置</span>';
                }
            } catch (error) {
                document.getElementById('currentSettings').innerHTML = `<span class="error">错误: ${error.message}</span>`;
            }
        }

        function clearSettings() {
            localStorage.removeItem(STORAGE_KEY);
            document.getElementById('currentSettings').innerHTML = '<span class="success">设置已清除</span>';
        }

        function testSaveSettings() {
            try {
                const testSettings = {
                    ...DEFAULT_SETTINGS,
                    immersiveMode: true,
                    testTimestamp: new Date().toISOString()
                };
                
                localStorage.setItem(STORAGE_KEY, JSON.stringify(testSettings));
                
                // 验证保存
                const saved = localStorage.getItem(STORAGE_KEY);
                const parsed = JSON.parse(saved);
                
                document.getElementById('testResults').innerHTML = 
                    `<span class="success">保存成功:</span>\n${JSON.stringify(parsed, null, 2)}`;
            } catch (error) {
                document.getElementById('testResults').innerHTML = `<span class="error">保存失败: ${error.message}</span>`;
            }
        }

        function testLoadSettings() {
            try {
                const stored = localStorage.getItem(STORAGE_KEY);
                if (stored) {
                    const parsedSettings = JSON.parse(stored);
                    const finalSettings = { ...DEFAULT_SETTINGS, ...parsedSettings };
                    
                    document.getElementById('testResults').innerHTML = 
                        `<span class="success">加载成功:</span>\n原始存储: ${stored}\n\n解析结果: ${JSON.stringify(parsedSettings, null, 2)}\n\n最终设置: ${JSON.stringify(finalSettings, null, 2)}`;
                } else {
                    document.getElementById('testResults').innerHTML = '<span class="error">没有找到存储的设置</span>';
                }
            } catch (error) {
                document.getElementById('testResults').innerHTML = `<span class="error">加载失败: ${error.message}</span>`;
            }
        }

        function saveManualSettings() {
            try {
                const immersiveMode = document.getElementById('immersiveMode').checked;
                const settings = {
                    ...DEFAULT_SETTINGS,
                    immersiveMode: immersiveMode
                };
                
                localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
                
                document.getElementById('manualResults').innerHTML = 
                    `<span class="success">手动设置已保存:</span>\nimmersiveMode: ${immersiveMode}\n\n完整设置:\n${JSON.stringify(settings, null, 2)}`;
            } catch (error) {
                document.getElementById('manualResults').innerHTML = `<span class="error">保存失败: ${error.message}</span>`;
            }
        }

        // 页面加载时显示当前设置
        window.onload = function() {
            showCurrentSettings();
        };
    </script>
</body>
</html>

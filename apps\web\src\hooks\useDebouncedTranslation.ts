'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import { api } from '@/lib/api';
import type { TranslationResponse } from '@ebook-platform/types';

interface UseDebouncedTranslationOptions {
  defaultDelay?: number;
  context?: string;
}

interface UseDebouncedTranslationReturn {
  translation: TranslationResponse | null;
  loading: boolean;
  error: string | null;
  translate: (text: string, sourceLanguage: string, targetLanguage: string, delay?: number) => void;
  retranslate: (text: string, sourceLanguage: string, targetLanguage: string) => void;
  clearTranslation: () => void;
}

export const useDebouncedTranslation = ({
  defaultDelay = 800,
  context
}: UseDebouncedTranslationOptions = {}): UseDebouncedTranslationReturn => {
  const [translation, setTranslation] = useState<TranslationResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastRequestRef = useRef<string>('');
  const abortControllerRef = useRef<AbortController | null>(null);

  // 实际的翻译函数
  const performTranslation = useCallback(async (
    text: string, 
    sourceLanguage: string, 
    targetLanguage: string
  ) => {
    if (!text.trim()) return;

    // 生成请求标识
    const requestKey = `${text.trim()}-${sourceLanguage}-${targetLanguage}`;
    
    // 如果是相同的请求且已有结果，直接返回
    if (lastRequestRef.current === requestKey && translation) {
      setLoading(false);
      return;
    }

    try {
      // 取消之前的请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // 创建新的AbortController
      abortControllerRef.current = new AbortController();

      setLoading(true);
      setError(null);
      
      console.log('🌐 防抖翻译执行:', text.substring(0, 30) + '...');

      const requestData = {
        text: text.trim(),
        sourceLanguage,
        targetLanguage,
        context
      };

      console.log('🌐 发送翻译请求:', requestData);

      // 根据文本长度智能选择翻译API
      let response;
      if (text.trim().length > 1000) {
        console.log('📄 使用批量翻译API');
        response = await api.translation.translateBatch(requestData);
      } else {
        console.log('📝 使用标准翻译API');
        response = await api.translation.translate(requestData);
      }

      // 检查请求是否被取消
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      if (response.success) {
        console.log('✅ 翻译成功:', response.data);
        setTranslation(response.data);
        lastRequestRef.current = requestKey;
      } else {
        console.error('❌ 翻译API返回失败:', response);
        setError(response.error || '翻译失败');
      }
    } catch (err: any) {
      // 忽略被取消的请求
      if (err.name === 'AbortError') {
        return;
      }

      console.error('翻译API调用失败:', err);
      console.error('错误详情:', {
        message: err.message,
        status: err.response?.status,
        data: err.response?.data,
        url: err.config?.url
      });

      let errorMessage = '翻译失败，请稍后重试';
      if (err.response?.status === 401) {
        errorMessage = '用户认证失败，请重新登录';
      } else if (err.response?.status === 500) {
        errorMessage = 'AI翻译服务暂时不可用';
      } else if (err.code === 'NETWORK_ERROR') {
        errorMessage = '网络连接失败，请检查网络';
      } else if (err.code === 'ECONNABORTED' || err.message?.includes('timeout')) {
        errorMessage = '翻译请求超时，文本可能过长，请尝试选择较短的内容';
      } else if (err.response?.status === 400) {
        errorMessage = err.response?.data?.error || '请求参数错误';
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [translation, context]);

  // 防抖翻译函数
  const translate = useCallback((
    text: string, 
    sourceLanguage: string, 
    targetLanguage: string, 
    delay: number = defaultDelay
  ) => {
    // 清除之前的定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // 立即显示加载状态
    setLoading(true);
    setError(null);

    // 设置新的定时器
    timeoutRef.current = setTimeout(() => {
      performTranslation(text, sourceLanguage, targetLanguage);
    }, delay);
  }, [defaultDelay, performTranslation]);

  // 重新翻译（清除缓存）
  const retranslate = useCallback((
    text: string, 
    sourceLanguage: string, 
    targetLanguage: string
  ) => {
    lastRequestRef.current = '';
    setTranslation(null);
    translate(text, sourceLanguage, targetLanguage, 200); // 重新翻译使用较短延迟
  }, [translate]);

  // 清除翻译结果
  const clearTranslation = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setTranslation(null);
    setLoading(false);
    setError(null);
    lastRequestRef.current = '';
  }, []);

  // 清理函数
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    translation,
    loading,
    error,
    translate,
    retranslate,
    clearTranslation
  };
};

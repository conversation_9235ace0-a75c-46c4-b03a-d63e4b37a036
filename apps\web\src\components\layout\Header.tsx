'use client';

import React, { useState } from 'react';
import { Book<PERSON><PERSON>, <PERSON>u, <PERSON>ting<PERSON>, Moon, Sun, Palette, User, LogOut } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useAppActions, useTheme, useSidebarOpen } from '@/store';
import { useAuth } from '@/contexts/AuthContext';

export const Header: React.FC = () => {
  const theme = useTheme();
  const sidebarOpen = useSidebarOpen();
  const { setTheme, setSidebarOpen } = useAppActions();
  const { user, isAuthenticated, logout } = useAuth();
  const router = useRouter();
  const [userMenuOpen, setUserMenuOpen] = useState(false);

  const toggleTheme = () => {
    const themes = ['light', 'dark', 'sepia'] as const;
    const currentIndex = themes.indexOf(theme);
    const nextTheme = themes[(currentIndex + 1) % themes.length];
    setTheme(nextTheme);
  };

  const getThemeIcon = () => {
    switch (theme) {
      case 'dark':
        return <Moon className="h-5 w-5" />;
      case 'sepia':
        return <Palette className="h-5 w-5" />;
      default:
        return <Sun className="h-5 w-5" />;
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        {/* 左侧：菜单按钮和Logo */}
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="inline-flex items-center justify-center rounded-md p-2 text-muted-foreground hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
            aria-label="打开菜单"
          >
            <Menu className="h-5 w-5" />
          </button>
          
          <div className="flex items-center space-x-2">
            <BookOpen className="h-6 w-6 text-primary" />
            <span className="hidden font-bold sm:inline-block">
              智能书库
            </span>
          </div>
        </div>

        {/* 中间：搜索框（后续添加） */}
        <div className="flex flex-1 items-center justify-center px-6">
          {/* 搜索功能预留位置 */}
        </div>

        {/* 右侧：主题切换、用户菜单和设置 */}
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleTheme}
            className="inline-flex items-center justify-center rounded-md p-2 text-muted-foreground hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
            aria-label="切换主题"
            title={`当前主题: ${theme === 'light' ? '明亮' : theme === 'dark' ? '暗黑' : '护眼'}`}
          >
            {getThemeIcon()}
          </button>

          {/* 用户菜单 */}
          {isAuthenticated ? (
            <div className="relative">
              <button
                onClick={() => setUserMenuOpen(!userMenuOpen)}
                className="inline-flex items-center justify-center rounded-md p-2 text-muted-foreground hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
                aria-label="用户菜单"
              >
                <User className="h-5 w-5" />
              </button>

              {userMenuOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border">
                  <div className="px-4 py-2 text-sm text-gray-700 border-b">
                    <div className="font-medium">{user?.displayName || user?.username}</div>
                    <div className="text-gray-500">{user?.email}</div>
                  </div>
                  <button
                    onClick={async () => {
                      await logout();
                      setUserMenuOpen(false);
                      router.push('/login');
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    登出
                  </button>
                </div>
              )}
            </div>
          ) : (
            <button
              onClick={() => router.push('/login')}
              className="inline-flex items-center justify-center rounded-md px-3 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              登录
            </button>
          )}

          <button
            className="inline-flex items-center justify-center rounded-md p-2 text-muted-foreground hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
            aria-label="设置"
          >
            <Settings className="h-5 w-5" />
          </button>
        </div>
      </div>
    </header>
  );
};

'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { AuthState, User, RegisterRequest, LoginRequest } from '@/types/auth';
import { register, login, getCurrentUser, logout as logoutApi, validateToken } from '@/lib/auth-api';

// 认证状态
interface AuthContextType extends AuthState {
  register: (data: RegisterRequest) => Promise<{ success: boolean; error?: string }>;
  login: (data: LoginRequest) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

// 认证动作类型
type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_USER'; payload: { user: User; token: string } }
  | { type: 'CLEAR_USER' }
  | { type: 'SET_TOKEN'; payload: string };

// 初始状态
const initialState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: true,
};

// Reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_USER':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
      };
    case 'CLEAR_USER':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
      };
    case 'SET_TOKEN':
      return { ...state, token: action.payload };
    default:
      return state;
  }
}

// 创建Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Token存储键
const TOKEN_STORAGE_KEY = 'auth_token';

// AuthProvider组件
export function AuthProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // 从localStorage获取token
  const getStoredToken = (): string | null => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(TOKEN_STORAGE_KEY);
  };

  // 保存token到localStorage
  const setStoredToken = (token: string) => {
    if (typeof window === 'undefined') return;
    localStorage.setItem(TOKEN_STORAGE_KEY, token);
  };

  // 清除token
  const clearStoredToken = () => {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(TOKEN_STORAGE_KEY);
  };

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      const storedToken = getStoredToken();
      
      if (!storedToken) {
        dispatch({ type: 'SET_LOADING', payload: false });
        return;
      }

      // 验证token是否有效
      const isValid = await validateToken(storedToken);
      
      if (!isValid) {
        clearStoredToken();
        dispatch({ type: 'SET_LOADING', payload: false });
        return;
      }

      // 获取用户信息
      const userResult = await getCurrentUser(storedToken);
      
      if (userResult.success && userResult.user) {
        dispatch({
          type: 'SET_USER',
          payload: { user: userResult.user, token: storedToken }
        });
      } else {
        clearStoredToken();
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    initAuth();
  }, []);

  // 注册函数
  const handleRegister = async (data: RegisterRequest): Promise<{ success: boolean; error?: string }> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    const result = await register(data);
    
    if (result.success && result.data) {
      setStoredToken(result.data.token);
      dispatch({
        type: 'SET_USER',
        payload: { user: result.data.user, token: result.data.token }
      });
      return { success: true };
    } else {
      dispatch({ type: 'SET_LOADING', payload: false });
      return { success: false, error: result.message };
    }
  };

  // 登录函数
  const handleLogin = async (data: LoginRequest): Promise<{ success: boolean; error?: string }> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    const result = await login(data);
    
    if (result.success && result.data) {
      setStoredToken(result.data.token);
      dispatch({
        type: 'SET_USER',
        payload: { user: result.data.user, token: result.data.token }
      });
      return { success: true };
    } else {
      dispatch({ type: 'SET_LOADING', payload: false });
      return { success: false, error: result.message };
    }
  };

  // 登出函数
  const handleLogout = async () => {
    if (state.token) {
      await logoutApi(state.token);
    }
    
    clearStoredToken();
    dispatch({ type: 'CLEAR_USER' });
  };

  // 刷新用户信息
  const refreshUser = async () => {
    if (!state.token) return;
    
    const userResult = await getCurrentUser(state.token);
    
    if (userResult.success && userResult.user) {
      dispatch({
        type: 'SET_USER',
        payload: { user: userResult.user, token: state.token }
      });
    }
  };

  const contextValue: AuthContextType = {
    ...state,
    register: handleRegister,
    login: handleLogin,
    logout: handleLogout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// 使用认证Context的Hook
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

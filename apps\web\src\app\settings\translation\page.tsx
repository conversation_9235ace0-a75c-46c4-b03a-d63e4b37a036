'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { Languages, Save, RotateCcw, ArrowLeft } from 'lucide-react';
import { api } from '@/lib/api';
import { useTranslationSettings } from '@/hooks/useTranslationSettings';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import type { Language } from '@ebook-platform/types';

export default function TranslationSettingsPage() {
  const router = useRouter();
  const { settings, updateSettings, resetSettings, isLoading } = useTranslationSettings();
  const [languages, setLanguages] = useState<Language[]>([]);
  const [languagesLoading, setLanguagesLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // 获取支持的语言列表
  useEffect(() => {
    const fetchLanguages = async () => {
      try {
        setLanguagesLoading(true);
        const response = await api.translation.getLanguages();
        if (response.success) {
          setLanguages(response.data || []);
        }
      } catch (err) {
        console.error('获取语言列表失败:', err);
      } finally {
        setLanguagesLoading(false);
      }
    };
    fetchLanguages();
  }, []);

  // 保存设置
  const handleSave = async () => {
    setSaving(true);
    try {
      // 设置已经通过updateSettings自动保存到localStorage了
      // 这里只需要给用户反馈
      await new Promise(resolve => setTimeout(resolve, 500));
      // 可以在这里添加成功提示
      console.log('翻译设置已保存');
    } catch (error) {
      console.error('保存设置失败:', error);
    } finally {
      setSaving(false);
    }
  };

  // 重置设置
  const handleReset = () => {
    if (confirm('确定要重置所有翻译设置吗？')) {
      resetSettings();
    }
  };

  if (isLoading || languagesLoading) {
    return (
      <ProtectedRoute>
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
              <p className="text-gray-600">加载中...</p>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* 页面头部 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center gap-4 mb-6">
            <button
              onClick={() => router.back()}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Languages className="h-8 w-8 text-indigo-600" />
                翻译设置
              </h1>
              <p className="text-gray-600 mt-2">配置划词翻译的默认选项</p>
            </div>
          </div>
        </motion.div>

        {/* 设置表单 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-md border"
        >
          <div className="p-6 border-b border-gray-100">
            <h2 className="text-lg font-semibold text-gray-900">语言设置</h2>
            <p className="text-sm text-gray-600 mt-1">设置划词翻译的默认源语言和目标语言</p>
          </div>

          <div className="p-6 space-y-6">
            {/* 启用划词翻译 */}
            <div>
              <div className="flex items-center justify-between">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    启用划词翻译
                  </label>
                  <p className="text-xs text-gray-500 mt-1">
                    开启后，在阅读页面选中文本时会自动显示翻译弹窗
                  </p>
                </div>
                <div className="ml-4">
                  <button
                    type="button"
                    onClick={() => updateSettings({ enabled: !settings.enabled })}
                    className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                      settings.enabled ? 'bg-indigo-600' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                        settings.enabled ? 'translate-x-5' : 'translate-x-0'
                      }`}
                    />
                  </button>
                </div>
              </div>
            </div>

            {/* 沉浸式翻译模式 */}
            <div>
              <div className="flex items-center justify-between">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    沉浸式翻译模式
                  </label>
                  <p className="text-xs text-gray-500 mt-1">
                    开启后，阅读页面将显示左右对照的原文和翻译
                  </p>
                </div>
                <div className="ml-4">
                  <button
                    type="button"
                    onClick={() => updateSettings({ immersiveMode: !settings.immersiveMode })}
                    className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                      settings.immersiveMode ? 'bg-indigo-600' : 'bg-gray-200'
                    }`}
                    disabled={!settings.enabled}
                  >
                    <span
                      className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                        settings.immersiveMode ? 'translate-x-5' : 'translate-x-0'
                      }`}
                    />
                  </button>
                </div>
              </div>
            </div>

            {/* 沉浸式模式语言设置 */}
            {settings.immersiveMode && (
              <div className="space-y-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="text-sm font-medium text-blue-900">沉浸式模式语言设置</h4>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs font-medium text-blue-700 mb-1">
                      沉浸式源语言
                    </label>
                    <select
                      value={settings.immersiveSourceLanguage}
                      onChange={(e) => updateSettings({ immersiveSourceLanguage: e.target.value })}
                      className="w-full text-sm px-2 py-1 border border-blue-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                    >
                      {languages.map(lang => (
                        <option key={lang.code} value={lang.code}>
                          {lang.flag} {lang.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-blue-700 mb-1">
                      沉浸式目标语言
                    </label>
                    <select
                      value={settings.immersiveTargetLanguage}
                      onChange={(e) => updateSettings({ immersiveTargetLanguage: e.target.value })}
                      className="w-full text-sm px-2 py-1 border border-blue-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                    >
                      {languages.filter(lang => lang.code !== 'auto').map(lang => (
                        <option key={lang.code} value={lang.code}>
                          {lang.flag} {lang.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <p className="text-xs text-blue-600">
                  💡 沉浸式模式将整个章节内容进行翻译，适合学习外语或深度阅读
                </p>
              </div>
            )}

            {/* 分隔线 */}
            <div className="border-t border-gray-200"></div>

            {/* 默认源语言 */}
            <div className={settings.enabled ? '' : 'opacity-50 pointer-events-none'}>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                默认源语言
              </label>
              <select
                value={settings.defaultSourceLanguage}
                onChange={(e) => updateSettings({ defaultSourceLanguage: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              >
                {languages.map(lang => (
                  <option key={lang.code} value={lang.code}>
                    {lang.flag} {lang.name}
                  </option>
                ))}
              </select>
              <p className="text-xs text-gray-500 mt-1">
                选择"自动检测"可以让AI自动识别文本语言
              </p>
            </div>

            {/* 默认目标语言 */}
            <div className={settings.enabled ? '' : 'opacity-50 pointer-events-none'}>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                默认目标语言
              </label>
              <select
                value={settings.defaultTargetLanguage}
                onChange={(e) => updateSettings({ defaultTargetLanguage: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              >
                {languages.filter(lang => lang.code !== 'auto').map(lang => (
                  <option key={lang.code} value={lang.code}>
                    {lang.flag} {lang.name}
                  </option>
                ))}
              </select>
              <p className="text-xs text-gray-500 mt-1">
                选中文本时将翻译成此语言
              </p>
            </div>

            {/* 其他选项 */}
            <div className={`space-y-4 ${settings.enabled ? '' : 'opacity-50 pointer-events-none'}`}>
              <h3 className="text-sm font-medium text-gray-700">其他选项</h3>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="autoDetectLanguage"
                  checked={settings.autoDetectLanguage}
                  onChange={(e) => updateSettings({ autoDetectLanguage: e.target.checked })}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="autoDetectLanguage" className="ml-2 text-sm text-gray-700">
                  自动检测语言
                </label>
              </div>
              <p className="text-xs text-gray-500 ml-6">
                启用后，即使设置了源语言，也会尝试自动检测文本的实际语言
              </p>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="showOriginalText"
                  checked={settings.showOriginalText}
                  onChange={(e) => updateSettings({ showOriginalText: e.target.checked })}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="showOriginalText" className="ml-2 text-sm text-gray-700">
                  显示原文
                </label>
              </div>
              <p className="text-xs text-gray-500 ml-6">
                在翻译弹窗中同时显示原文和译文
              </p>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="p-6 border-t border-gray-100 flex items-center justify-between">
            <button
              onClick={handleReset}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              重置设置
            </button>

            <button
              onClick={handleSave}
              disabled={saving}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              {saving ? '保存中...' : '保存设置'}
            </button>
          </div>
        </motion.div>

        {/* 使用说明 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6"
        >
          <h3 className="text-lg font-semibold text-blue-900 mb-3">使用说明</h3>
          <div className="space-y-2 text-sm text-blue-800">
            <p>• <strong>划词翻译</strong>：在阅读页面选中任意文本，会自动弹出翻译窗口</p>
            <p>• <strong>沉浸式翻译</strong>：开启后整个章节将以左右对照的方式显示原文和翻译</p>
            <p>• <strong>快捷操作</strong>：翻译窗口支持复制译文、语音朗读、重新翻译等功能</p>
            <p>• <strong>语言切换</strong>：可以在翻译窗口或沉浸式界面中切换源语言和目标语言</p>
            <p>• <strong>上下文理解</strong>：AI会根据书籍和章节信息提供更准确的翻译</p>
            <p>• <strong>快捷键</strong>：按 ESC 键可以快速关闭翻译窗口</p>
            <p>• <strong>模式切换</strong>：可以在阅读设置中快速切换划词翻译和沉浸式翻译模式</p>
          </div>
        </motion.div>
      </div>
    </ProtectedRoute>
  );
}

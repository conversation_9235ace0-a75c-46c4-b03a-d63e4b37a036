// 测试正则表达式匹配
const testText = `第一章山边小村

二愣子睁大着双眼，直直望着茅草和烂泥糊成的黑屋顶，身上盖着的旧棉被，已呈深黄色，看不出原来的本来面目，还若有若无的散着淡淡的霉味。

第二章 修仙之路

韩立开始了他的修仙之路...

第三章 神秘老者

在山洞中，韩立遇到了一位神秘的老者...

第四章 炼气期

韩立开始修炼炼气期功法...

第五章 筑基丹

为了筑基，韩立需要寻找筑基丹...`;

console.log('🧪 测试正则表达式匹配...');
console.log('📝 测试文本:');
console.log(testText);
console.log('\n' + '='.repeat(50) + '\n');

const chapterPatterns = [
  /^第[一二三四五六七八九十百千万\d]+章\s*.*/gm,
  /^第[一二三四五六七八九十百千万\d]+节\s*.*/gm,
  /^Chapter\s+\d+.*/gim,
  /^\d+\.\s*.*/gm,
  /^[一二三四五六七八九十百千万]+、.*/gm
];

for (let i = 0; i < chapterPatterns.length; i++) {
  const pattern = chapterPatterns[i];
  console.log(`🧪 测试模式 ${i + 1}: ${pattern}`);
  
  const matches = Array.from(testText.matchAll(pattern));
  console.log(`📊 匹配结果: ${matches.length} 个`);
  
  if (matches.length > 0) {
    matches.forEach((match, index) => {
      console.log(`  ${index + 1}. "${match[0].trim()}" (位置: ${match.index})`);
    });
  }
  
  console.log('\n' + '-'.repeat(30) + '\n');
}

// 测试单独的章节标题
const singleTests = [
  '第一章山边小村',
  '第二章 修仙之路',
  '第三章 神秘老者',
  '第四章 炼气期',
  '第五章 筑基丹'
];

console.log('🔍 单独测试章节标题:');
const pattern = /^第[一二三四五六七八九十百千万\d]+章\s*.*/gm;

singleTests.forEach((title, index) => {
  const match = pattern.test(title);
  console.log(`${index + 1}. "${title}" -> ${match ? '✅ 匹配' : '❌ 不匹配'}`);
  pattern.lastIndex = 0; // 重置正则表达式状态
});

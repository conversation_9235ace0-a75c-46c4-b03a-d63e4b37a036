'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { BookOpen, Plus, Search, Filter, Calendar, User, FileText, Trash2, Edit } from 'lucide-react';
import { api } from '@/lib/api';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import type { Book } from '@ebook-platform/types';

export default function BooksPage() {
  const router = useRouter();
  const [books, setBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // 获取书籍列表
  useEffect(() => {
    const fetchBooks = async () => {
      try {
        setLoading(true);
        const response = await api.books.list();
        if (response.success) {
          setBooks(response.data || []);
        } else {
          setError(response.message || '获取书籍列表失败');
        }
      } catch (err) {
        setError('网络错误，请稍后重试');
        console.error('获取书籍列表失败:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchBooks();
  }, []);

  // 过滤书籍
  const filteredBooks = books.filter(book =>
    book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (book.author && book.author.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // 删除书籍
  const handleDeleteBook = async (bookId: string) => {
    if (!confirm('确定要删除这本书吗？此操作不可恢复。')) {
      return;
    }

    try {
      const response = await api.books.delete(bookId);
      if (response.success) {
        setBooks(books.filter(book => book.id !== bookId));
      } else {
        alert('删除失败：' + (response.message || '未知错误'));
      }
    } catch (err) {
      alert('删除失败，请稍后重试');
      console.error('删除书籍失败:', err);
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <ProtectedRoute>
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* 页面头部 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <BookOpen className="h-8 w-8 text-indigo-600" />
                我的书库
              </h1>
              <p className="text-gray-600 mt-2">管理您的电子书收藏</p>
            </div>
            
            <button
              onClick={() => router.push('/upload')}
              className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              <Plus className="h-5 w-5 mr-2" />
              上传新书
            </button>
          </div>

          {/* 搜索栏 */}
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="搜索书名或作者..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              />
            </div>
          </div>
        </motion.div>

        {/* 内容区域 */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
              <p className="text-gray-600">加载中...</p>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
              <p className="text-red-600 mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                重试
              </button>
            </div>
          </div>
        ) : filteredBooks.length === 0 ? (
          <div className="text-center py-12">
            <BookOpen className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              {searchTerm ? '没有找到匹配的书籍' : '还没有上传任何书籍'}
            </h3>
            <p className="text-gray-600 mb-6">
              {searchTerm ? '尝试使用其他关键词搜索' : '上传您的第一本电子书开始阅读之旅'}
            </p>
            {!searchTerm && (
              <button
                onClick={() => router.push('/upload')}
                className="inline-flex items-center px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              >
                <Plus className="h-5 w-5 mr-2" />
                上传书籍
              </button>
            )}
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {filteredBooks.map((book, index) => (
              <motion.div
                key={book.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200"
              >
                <div className="p-6">
                  {/* 书籍信息 */}
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                      {book.title}
                    </h3>
                    {book.author && (
                      <p className="text-sm text-gray-600 flex items-center gap-1 mb-2">
                        <User className="h-4 w-4" />
                        {book.author}
                      </p>
                    )}
                    <p className="text-sm text-gray-500 flex items-center gap-1 mb-2">
                      <Calendar className="h-4 w-4" />
                      {formatDate(book.uploadedAt)}
                    </p>
                    <p className="text-sm text-gray-500 flex items-center gap-1">
                      <FileText className="h-4 w-4" />
                      {book.totalWords ? `${book.totalWords.toLocaleString()} 字` : '未知长度'}
                    </p>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => router.push(`/read/${book.id}`)}
                      className="flex-1 px-3 py-2 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 transition-colors"
                    >
                      开始阅读
                    </button>
                    <button
                      onClick={() => handleDeleteBook(book.id)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                      title="删除书籍"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* 统计信息 */}
        {!loading && !error && books.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mt-8 text-center text-sm text-gray-500"
          >
            共 {books.length} 本书籍
            {searchTerm && filteredBooks.length !== books.length && (
              <span>，显示 {filteredBooks.length} 本匹配结果</span>
            )}
          </motion.div>
        )}
      </div>
    </ProtectedRoute>
  );
}

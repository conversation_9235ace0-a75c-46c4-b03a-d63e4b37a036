import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { Book, ReadingSettings, ReadingProgress } from '@ebook-platform/types';

// 应用状态接口
interface AppState {
  // 书籍状态
  books: Book[];
  currentBook: Book | null;
  isLoading: boolean;
  error: string | null;
  
  // 阅读设置
  readingSettings: ReadingSettings;
  
  // 阅读进度
  readingProgress: Record<string, ReadingProgress>;
  
  // UI状态
  sidebarOpen: boolean;
  theme: 'light' | 'dark' | 'sepia';
  
  // Actions
  setBooks: (books: Book[]) => void;
  setCurrentBook: (book: Book | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  updateReadingSettings: (settings: Partial<ReadingSettings>) => void;
  updateReadingProgress: (bookId: string, progress: ReadingProgress) => void;
  setSidebarOpen: (open: boolean) => void;
  setTheme: (theme: 'light' | 'dark' | 'sepia') => void;
  resetState: () => void;
}

// 默认阅读设置
const defaultReadingSettings: ReadingSettings = {
  fontSize: 18,
  fontFamily: 'Georgia',
  lineHeight: 1.8,
  theme: 'light',
  backgroundColor: '#ffffff',
  textColor: '#1a1a1a',
  maxWidth: 800,
};

// 创建状态存储
export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        books: [],
        currentBook: null,
        isLoading: false,
        error: null,
        readingSettings: defaultReadingSettings,
        readingProgress: {},
        sidebarOpen: false,
        theme: 'light',
        
        // Actions
        setBooks: (books) => set({ books }, false, 'setBooks'),
        
        setCurrentBook: (book) => set({ currentBook: book }, false, 'setCurrentBook'),
        
        setLoading: (loading) => set({ isLoading: loading }, false, 'setLoading'),
        
        setError: (error) => set({ error }, false, 'setError'),
        
        updateReadingSettings: (settings) => 
          set(
            (state) => ({
              readingSettings: { ...state.readingSettings, ...settings }
            }),
            false,
            'updateReadingSettings'
          ),
        
        updateReadingProgress: (bookId, progress) =>
          set(
            (state) => ({
              readingProgress: {
                ...state.readingProgress,
                [bookId]: progress
              }
            }),
            false,
            'updateReadingProgress'
          ),
        
        setSidebarOpen: (open) => set({ sidebarOpen: open }, false, 'setSidebarOpen'),
        
        setTheme: (theme) => {
          // 更新主题时同时更新阅读设置
          const themeColors = {
            light: { backgroundColor: '#ffffff', textColor: '#1a1a1a' },
            dark: { backgroundColor: '#1a1a1a', textColor: '#e5e5e5' },
            sepia: { backgroundColor: '#f4f1ea', textColor: '#5c4b37' }
          };
          
          set(
            (state) => ({
              theme,
              readingSettings: {
                ...state.readingSettings,
                theme,
                ...themeColors[theme]
              }
            }),
            false,
            'setTheme'
          );
        },
        
        resetState: () => 
          set(
            {
              books: [],
              currentBook: null,
              isLoading: false,
              error: null,
              readingProgress: {},
              sidebarOpen: false,
            },
            false,
            'resetState'
          ),
      }),
      {
        name: 'ebook-platform-storage',
        // 只持久化部分状态
        partialize: (state) => ({
          readingSettings: state.readingSettings,
          readingProgress: state.readingProgress,
          theme: state.theme,
        }),
      }
    ),
    {
      name: 'ebook-platform-store',
    }
  )
);

// 选择器 hooks
export const useBooks = () => useAppStore((state) => state.books);
export const useCurrentBook = () => useAppStore((state) => state.currentBook);
export const useLoading = () => useAppStore((state) => state.isLoading);
export const useError = () => useAppStore((state) => state.error);
export const useReadingSettings = () => useAppStore((state) => state.readingSettings);
export const useReadingProgress = (bookId?: string) => 
  useAppStore((state) => bookId ? state.readingProgress[bookId] : state.readingProgress);
export const useSidebarOpen = () => useAppStore((state) => state.sidebarOpen);
export const useTheme = () => useAppStore((state) => state.theme);

// Actions hooks
export const useAppActions = () => {
  const store = useAppStore();
  return {
    setBooks: store.setBooks,
    setCurrentBook: store.setCurrentBook,
    setLoading: store.setLoading,
    setError: store.setError,
    updateReadingSettings: store.updateReadingSettings,
    updateReadingProgress: store.updateReadingProgress,
    setSidebarOpen: store.setSidebarOpen,
    setTheme: store.setTheme,
    resetState: store.resetState,
  };
};

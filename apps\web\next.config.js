/** @type {import('next').NextConfig} */
const nextConfig = {
  // 实验性功能
  experimental: {
    // 启用SWC压缩
    swcMinify: true
  },
  
  // 编译配置
  typescript: {
    // 构建时忽略类型错误（开发时仍会显示）
    ignoreBuildErrors: false
  },
  
  // 图片优化
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif']
  },
  
  // API路由配置
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/:path*`
      }
    ];
  },
  
  // 环境变量
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'
  },
  
  // 输出配置
  output: 'standalone',
  
  // 性能优化
  compress: true,
  
  // 开发配置
  devIndicators: {
    buildActivity: true,
    buildActivityPosition: 'bottom-right'
  },
  
  // 静态文件
  trailingSlash: false,
  
  // 安全头
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin'
          }
        ]
      }
    ];
  }
};

module.exports = nextConfig;

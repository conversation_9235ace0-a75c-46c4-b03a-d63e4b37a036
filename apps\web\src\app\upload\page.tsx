'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { BookOpen, ArrowLeft, CheckCircle, AlertCircle, X } from 'lucide-react';
import { FileUpload } from '@/components/upload/FileUpload';
import { useAppActions } from '@/store';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import type { Book } from '@ebook-platform/types';

export default function UploadPage() {
  const router = useRouter();
  const { setBooks } = useAppActions();
  const [uploadedBooks, setUploadedBooks] = useState<Book[]>([]);
  const [error, setError] = useState<string | null>(null);

  // 处理上传成功（去重：同一书籍只保留一份，且用最新数据覆盖）
  const handleUploadSuccess = (book: Book) => {
    setUploadedBooks(prev => {
      const idx = prev.findIndex(b => b.id === book.id);
      if (idx >= 0) {
        const next = prev.slice();
        next[idx] = { ...prev[idx], ...book };
        return next;
      }
      return [...prev, book];
    });
    setError(null);

    // 更新全局状态（可选，也可以在书库页面重新获取）
    // 这里暂时不更新，让用户在书库页面看到最新数据
  };

  // 处理上传错误
  const handleUploadError = (errorMessage: string) => {
    setError(errorMessage);
  };

  // 清除错误
  const clearError = () => {
    setError(null);
  };

  // 跳转到书库
  const goToLibrary = () => {
    router.push('/books');
  };

  // 跳转到阅读
  const goToReading = (bookId: string) => {
    router.push(`/read/${bookId}`);
  };

  return (
    <ProtectedRoute>
      <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* 页面头部 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex items-center space-x-4 mb-6">
          <button
            onClick={() => router.back()}
            className="inline-flex items-center px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </button>
        </div>
        
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">上传电子书</h1>
          <p className="text-muted-foreground text-lg">
            上传您的txt格式电子书，开始智能阅读之旅
          </p>
        </div>
      </motion.div>

      {/* 错误提示 */}
      {error && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start space-x-3"
        >
          <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
          <div className="flex-1">
            <p className="text-red-800 font-medium">上传失败</p>
            <p className="text-red-700 text-sm mt-1">{error}</p>
          </div>
          <button
            onClick={clearError}
            className="text-red-500 hover:text-red-700"
          >
            <X className="h-4 w-4" />
          </button>
        </motion.div>
      )}

      {/* 文件上传组件 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <FileUpload
          onUploadSuccess={handleUploadSuccess}
          onUploadError={handleUploadError}
          className="mb-8"
        />
      </motion.div>

      {/* 上传成功列表 */}
      {uploadedBooks.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
            上传成功 ({uploadedBooks.length})
          </h2>
          
          <div className="space-y-3">
            {uploadedBooks.map((book, index) => (
              <motion.div
                key={book.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <BookOpen className="h-8 w-8 text-green-600" />
                  <div>
                    <h3 className="font-medium text-green-900">{book.title}</h3>
                    <p className="text-sm text-green-700">
                      {book.chapterCount > 0 ? `${book.chapterCount} 章节` : '解析中...'}
                      {book.totalWords > 0 && ` • ${Math.round(book.totalWords / 1000)}k 字`}
                    </p>
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <button
                    onClick={() => goToReading(book.id)}
                    disabled={book.status !== 'ready'}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {book.status === 'ready' ? '开始阅读' : '解析中...'}
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* 使用说明 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-muted/50 rounded-lg p-6"
      >
        <h3 className="font-semibold mb-3">使用说明</h3>
        <ul className="space-y-2 text-sm text-muted-foreground">
          <li>• 支持 .txt 格式的文本文件</li>
          <li>• 文件大小不超过 50MB</li>
          <li>• 系统会自动识别章节结构</li>
          <li>• 上传后会自动解析文本内容</li>
          <li>• 解析完成后即可开始阅读</li>
        </ul>
        
        <div className="mt-4 pt-4 border-t border-muted">
          <p className="text-sm text-muted-foreground">
            上传完成后，您可以在
            <button
              onClick={goToLibrary}
              className="text-primary hover:underline mx-1"
            >
              书库页面
            </button>
            查看和管理您的电子书。
          </p>
        </div>
      </motion.div>
      </div>
    </ProtectedRoute>
  );
}

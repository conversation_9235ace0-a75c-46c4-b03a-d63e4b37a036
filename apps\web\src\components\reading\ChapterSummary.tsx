"use client";

import { useState, useEffect } from 'react';
import { api } from '@/lib/api';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface ChapterSummaryData {
  chapterTitle: string;
  summary: string;
  wordCount: number;
  generatedAt: string;
}

interface ChapterSummaryProps {
  bookId: string;
  currentChapterIndex: number;
  chapterTitle: string;
  theme: 'light' | 'dark' | 'sepia';
}

export default function ChapterSummary({ 
  bookId, 
  currentChapterIndex, 
  chapterTitle,
  theme 
}: ChapterSummaryProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [summaryData, setSummaryData] = useState<ChapterSummaryData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getThemeClasses = () => {
    switch (theme) {
      case 'dark':
        return {
          button: 'bg-gray-800 text-white hover:bg-gray-700 border-gray-600',
          modal: 'bg-gray-800 text-white',
          content: 'bg-gray-700',
          border: 'border-gray-600'
        };
      case 'sepia':
        return {
          button: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200 border-yellow-300',
          modal: 'bg-yellow-50 text-yellow-900',
          content: 'bg-yellow-100',
          border: 'border-yellow-300'
        };
      default:
        return {
          button: 'bg-white text-gray-700 hover:bg-gray-50 border-gray-300',
          modal: 'bg-white text-gray-900',
          content: 'bg-gray-50',
          border: 'border-gray-300'
        };
    }
  };

  const themeClasses = getThemeClasses();

  const generateSummary = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api'}/books/${bookId}/chapters/${currentChapterIndex}/summary`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          'Accept': 'application/json; charset=utf-8',
        },
        body: JSON.stringify({})
      });
      
      const result = await response.json();
      
      if (result.success) {
        setSummaryData(result.data);
      } else {
        setError(result.error || '生成总结失败');
      }
    } catch (err) {
      setError('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 章节切换时自动生成总结
  useEffect(() => {
    if (isOpen) {
      generateSummary();
    }
  }, [currentChapterIndex, bookId]);

  const toggleSummary = () => {
    if (!isOpen) {
      setIsOpen(true);
      if (!summaryData) {
        generateSummary();
      }
    } else {
      setIsOpen(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <>
      {/* 右下角浮动按钮 */}
      <div className="fixed bottom-6 right-6 z-40">
        <button
          onClick={toggleSummary}
          className={`p-3 rounded-full shadow-lg transition-all hover:scale-110 ${themeClasses.button}`}
          title="AI章节总结"
        >
          🤖
        </button>
      </div>

      {/* 总结模态框 */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`rounded-lg w-full max-w-2xl max-h-[80vh] overflow-hidden shadow-2xl ${themeClasses.modal}`}>
            {/* 头部 */}
            <div className={`p-4 border-b ${themeClasses.border}`}>
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-semibold">🤖 AI章节总结</h3>
                  <p className="text-sm opacity-70 mt-1">{chapterTitle}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={generateSummary}
                    disabled={loading}
                    className={`px-3 py-1 text-sm rounded border transition-colors ${themeClasses.border} ${
                      loading ? 'opacity-50 cursor-not-allowed' : 'hover:opacity-80'
                    }`}
                    title="重新生成总结"
                  >
                    {loading ? '生成中...' : '🔄 刷新'}
                  </button>
                  <button 
                    onClick={() => setIsOpen(false)} 
                    className="text-xl hover:opacity-70"
                  >
                    ✕
                  </button>
                </div>
              </div>
            </div>

            {/* 内容区域 */}
            <div className="p-4 overflow-y-auto max-h-[60vh]">
              {loading && (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-current"></div>
                  <span className="ml-3">AI正在分析章节内容...</span>
                </div>
              )}

              {error && (
                <div className="text-center py-8">
                  <div className="text-red-500 mb-4">❌ {error}</div>
                  <button
                    onClick={generateSummary}
                    className={`px-4 py-2 rounded border ${themeClasses.border} hover:opacity-80`}
                  >
                    重试
                  </button>
                </div>
              )}

              {summaryData && !loading && (
                <div className="space-y-4">
                  <div className={`p-4 rounded-lg ${themeClasses.content}`}>
                    <h4 className="font-medium mb-2">📝 内容总结</h4>
                    <div className={`leading-relaxed text-sm prose prose-sm max-w-none ${
                      theme === 'dark' ? 'prose-invert' :
                      theme === 'sepia' ? 'prose-stone' : 'prose-gray'
                    }`}>
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        components={{
                          // 自定义样式以适应主题
                          h1: ({children}) => <h1 className="text-lg font-bold mb-3 mt-4 first:mt-0">{children}</h1>,
                          h2: ({children}) => <h2 className="text-base font-semibold mb-2 mt-3 first:mt-0">{children}</h2>,
                          h3: ({children}) => <h3 className="text-sm font-medium mb-2 mt-2 first:mt-0">{children}</h3>,
                          p: ({children}) => <p className="mb-3 leading-relaxed">{children}</p>,
                          ul: ({children}) => <ul className="list-disc list-inside mb-3 space-y-1 pl-2">{children}</ul>,
                          ol: ({children}) => <ol className="list-decimal list-inside mb-3 space-y-1 pl-2">{children}</ol>,
                          li: ({children}) => <li className="text-sm leading-relaxed">{children}</li>,
                          strong: ({children}) => <strong className="font-semibold text-current">{children}</strong>,
                          em: ({children}) => <em className="italic text-current">{children}</em>,
                          code: ({children}) => (
                            <code className={`px-1.5 py-0.5 rounded text-xs font-mono ${
                              theme === 'dark' ? 'bg-gray-800 text-gray-200' :
                              theme === 'sepia' ? 'bg-amber-100 text-amber-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {children}
                            </code>
                          ),
                          blockquote: ({children}) => (
                            <blockquote className={`border-l-3 pl-4 py-2 my-3 italic ${
                              theme === 'dark' ? 'border-gray-600 bg-gray-800/30' :
                              theme === 'sepia' ? 'border-amber-400 bg-amber-50/50' :
                              'border-gray-300 bg-gray-50/50'
                            }`}>
                              {children}
                            </blockquote>
                          )
                        }}
                      >
                        {summaryData.summary}
                      </ReactMarkdown>
                    </div>
                  </div>

                  <div className="flex justify-between items-center text-xs opacity-70">
                    <span>章节字数: {summaryData.wordCount.toLocaleString()} 字</span>
                    <span>生成时间: {formatDate(summaryData.generatedAt)}</span>
                  </div>

                  <div className={`p-3 rounded border ${themeClasses.border} text-xs opacity-80`}>
                    💡 提示：切换章节时会自动重新生成总结，您也可以点击"刷新"按钮手动更新。
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}

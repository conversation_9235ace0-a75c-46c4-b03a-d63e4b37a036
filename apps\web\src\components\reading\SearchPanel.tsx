"use client";

import { useState, useEffect, useRef } from 'react';

export interface SearchResult {
  chapterIndex: number;
  chapterTitle: string;
  position: number;
  context: string;
  matchStart: number;
  matchEnd: number;
}

interface SearchPanelProps {
  isOpen: boolean;
  onClose: () => void;
  bookId: string;
  chapters: Array<{ id: string; title: string; orderIndex: number }>;
  currentChapterIndex: number;
  currentContent: string;
  onJumpToResult: (chapterIndex: number, position: number) => void;
}

export default function SearchPanel({ 
  isOpen, 
  onClose, 
  bookId, 
  chapters, 
  currentChapterIndex, 
  currentContent,
  onJumpToResult 
}: SearchPanelProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchMode, setSearchMode] = useState<'current' | 'all'>('current');
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  const highlightText = (text: string, start: number, end: number) => {
    return (
      <>
        {text.slice(0, start)}
        <mark className="bg-yellow-200 px-1">{text.slice(start, end)}</mark>
        {text.slice(end)}
      </>
    );
  };

  const searchInText = (text: string, query: string, chapterIndex: number, chapterTitle: string): SearchResult[] => {
    if (!query.trim()) return [];
    
    const results: SearchResult[] = [];
    const lowerQuery = query.toLowerCase();
    const lowerText = text.toLowerCase();
    let position = 0;

    while (true) {
      const index = lowerText.indexOf(lowerQuery, position);
      if (index === -1) break;

      // 获取上下文（前后各50个字符）
      const contextStart = Math.max(0, index - 50);
      const contextEnd = Math.min(text.length, index + query.length + 50);
      const context = text.slice(contextStart, contextEnd);
      const matchStart = index - contextStart;
      const matchEnd = matchStart + query.length;

      results.push({
        chapterIndex,
        chapterTitle,
        position: index,
        context,
        matchStart,
        matchEnd
      });

      position = index + 1;
    }

    return results;
  };

  const performSearch = async () => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    setLoading(true);
    try {
      if (searchMode === 'current') {
        // 当前章节搜索
        const currentChapter = chapters[currentChapterIndex];
        if (currentChapter && currentContent) {
          const searchResults = searchInText(
            currentContent, 
            query, 
            currentChapterIndex, 
            currentChapter.title
          );
          setResults(searchResults);
        }
      } else {
        // 全文搜索
        const allResults: SearchResult[] = [];
        
        // 这里应该调用后端API获取所有章节内容进行搜索
        // 为了演示，我们先只搜索当前章节
        const currentChapter = chapters[currentChapterIndex];
        if (currentChapter && currentContent) {
          const searchResults = searchInText(
            currentContent, 
            query, 
            currentChapterIndex, 
            currentChapter.title
          );
          allResults.push(...searchResults);
        }
        
        setResults(allResults);
      }
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      performSearch();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-20 z-50">
      <div className="bg-white rounded-lg w-[600px] max-h-[70vh] flex flex-col">
        {/* 搜索头部 */}
        <div className="p-4 border-b">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-lg font-semibold">搜索</h3>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              ✕
            </button>
          </div>
          
          <div className="flex space-x-2 mb-3">
            <input
              ref={inputRef}
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="输入搜索关键词..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={performSearch}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? '搜索中...' : '搜索'}
            </button>
          </div>

          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="radio"
                value="current"
                checked={searchMode === 'current'}
                onChange={(e) => setSearchMode(e.target.value as 'current' | 'all')}
                className="mr-2"
              />
              当前章节
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                value="all"
                checked={searchMode === 'all'}
                onChange={(e) => setSearchMode(e.target.value as 'current' | 'all')}
                className="mr-2"
              />
              全文搜索
            </label>
          </div>
        </div>

        {/* 搜索结果 */}
        <div className="flex-1 overflow-y-auto p-4">
          {results.length === 0 && query && !loading && (
            <div className="text-center text-gray-500 py-8">
              未找到相关结果
            </div>
          )}
          
          {results.length > 0 && (
            <div className="space-y-3">
              <div className="text-sm text-gray-600 mb-3">
                找到 {results.length} 个结果
              </div>
              
              {results.map((result, index) => (
                <div
                  key={index}
                  onClick={() => onJumpToResult(result.chapterIndex, result.position)}
                  className="p-3 border border-gray-200 rounded hover:bg-gray-50 cursor-pointer"
                >
                  <div className="text-sm text-blue-600 font-medium mb-1">
                    {result.chapterTitle}
                  </div>
                  <div className="text-sm text-gray-700 leading-relaxed">
                    {highlightText(result.context, result.matchStart, result.matchEnd)}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Hook for search functionality
export function useSearch() {
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  const openSearch = () => setIsSearchOpen(true);
  const closeSearch = () => setIsSearchOpen(false);

  // 快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        openSearch();
      }
      if (e.key === 'Escape' && isSearchOpen) {
        closeSearch();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isSearchOpen]);

  return {
    isSearchOpen,
    openSearch,
    closeSearch
  };
}

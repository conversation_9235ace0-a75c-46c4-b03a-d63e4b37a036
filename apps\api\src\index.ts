import Fastify from 'fastify';
import cors from '@fastify/cors';
import multipart from '@fastify/multipart';
import staticFiles from '@fastify/static';
import env from '@fastify/env';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

// 设置编码环境
process.env.LANG = 'en_US.UTF-8';
process.env.LC_ALL = 'en_US.UTF-8';

// 数据库和缓存
import { prisma, checkDatabaseConnection, initializeDatabase } from './lib/database.js';
import { ensureUploadDir } from './lib/fileHandler.js';

// 路由
import { healthRoutes } from './routes/health.js';
import { booksRoutes } from './routes/books.js';
import { uploadRoutes } from './routes/upload.js';
import { testOpenAIRoutes } from './routes/test-openai.js';
import { chaptersRoutes } from './routes/chapters.js';
import { authRoutes } from './routes/auth.js';
import { translationRoutes } from './routes/translation.js';

// 获取当前文件目录（ES模块兼容）
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 环境变量配置
const envSchema = {
  type: 'object',
  required: [],
  properties: {
    NODE_ENV: {
      type: 'string',
      default: 'development'
    },
    API_PORT: {
      type: 'string',
      default: '3001'
    },
    API_HOST: {
      type: 'string',
      default: 'localhost'
    },
    // OpenAI Config
    OPENAI_API_KEY: { type: 'string', default: '' },
    OPENAI_BASE_URL: { type: 'string', default: '' },
    OPENAI_MODEL: { type: 'string', default: 'gpt-4o-mini' },
    DATABASE_URL: {
      type: 'string',
      default: 'file:../../../data/database.sqlite'
    },
    UPLOAD_DIR: {
      type: 'string',
      default: './data/uploads'
    },
    MAX_FILE_SIZE: {
      type: 'string',
      default: '52428800'
    },
    CHAPTER_MIN_GAP: {
      type: 'string',
      default: '30'
    },
    // JWT Config
    JWT_SECRET: {
      type: 'string',
      default: 'your-super-secret-jwt-key-change-in-production'
    },
    JWT_EXPIRES_IN: {
      type: 'string',
      default: '7d'
    }
  }
};

// 创建Fastify实例
const fastify = Fastify({
  logger: {
    level: process.env.LOG_LEVEL || 'info',
    transport: process.env.NODE_ENV === 'development' ? {
      // Force plain text without emojis to avoid codepage issues on Windows consoles
      target: 'pino-pretty',
      options: {
        colorize: false,
        translateTime: 'HH:MM:ss Z',
        singleLine: true,
        ignore: 'pid,hostname,reqId,req.hostname,req.remoteAddress,req.remotePort'
      }
    } : undefined
  }

// Optional: write structured JSON logs to file to guarantee UTF-8
// const pino = await import('pino');
// const fileLogger = pino.default({ level: process.env.LOG_LEVEL || 'info' }, pino.default.destination({ dest: 'logs/app.log', sync: false }));
// fastify.addHook('onRequest', (req) => fileLogger.info({ method: req.method, url: req.url, hostname: req.hostname }, 'incoming request'));

});

// 注册插件
async function registerPlugins() {
  // 环境变量
  // 优先加载 .env.local，其次回退到 .env
  const envLocalPath = path.join(process.cwd(), '.env.local');
  const dotenvConfig: any = fs.existsSync(envLocalPath) ? { path: envLocalPath } : true;

  await fastify.register(env, {
    schema: envSchema,
    dotenv: dotenvConfig
  });

  // CORS
  await fastify.register(cors, {
    origin: true, // 开发环境允许所有来源
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Origin', 'X-Requested-With', 'Accept']
  });

  // 文件上传
  await fastify.register(multipart, {
    limits: {
      fileSize: parseInt(process.env.MAX_FILE_SIZE || '52428800') // 50MB
    }
  });

  // 静态文件服务（用于提供上传的文件）
  await fastify.register(staticFiles, {
    root: path.join(process.cwd(), 'data', 'uploads'),
    prefix: '/uploads/',
    decorateReply: false
  });
}

// 注册路由
async function registerRoutes() {
  // API前缀
  // 认证路由（直接注册，不使用API前缀）
  await fastify.register(authRoutes, { prefix: '/api' });

  await fastify.register(async function (fastify) {
    // 健康检查路由
    await fastify.register(healthRoutes);

    // 书籍管理路由
    await fastify.register(booksRoutes);

    // 文件上传路由
    await fastify.register(uploadRoutes);

    // 章节路由
    await fastify.register(chaptersRoutes);

    // OpenAI测试路由
    await fastify.register(testOpenAIRoutes);

    // 翻译路由
    await fastify.register(translationRoutes);

  }, { prefix: '/api' });

  // 根路由
  fastify.get('/', async (request, reply) => {
    return {
      name: '电子书平台API',
      version: '0.1.0',
      status: 'running',
      timestamp: new Date().toISOString(),
      endpoints: {
        health: '/api/health',
        books: '/api/books',
        docs: '/api/docs'
      }
    };
  });
}

// 错误处理
function setupErrorHandling() {
  // 全局错误处理
  fastify.setErrorHandler(async (error, request, reply) => {
    fastify.log.error(error);

    // Validation errors
    if (error.validation) {
      reply.code(400);
      return {
        success: false,
        error: 'Validation Error',
        details: error.validation
      };
    }

    // Multipart errors
    if (error.code === 'FST_FILES_LIMIT') {
      reply.code(413);
      return {
        success: false,
        error: '文件大小超过限制'
      };
    }

    // Database errors
    if (error.code?.startsWith('P')) { // Prisma error codes
      reply.code(500);
      return {
        success: false,
        error: '数据库操作失败'
      };
    }

    // Default error
    reply.code(500);
    return {
      success: false,
      error: process.env.NODE_ENV === 'development'
        ? error.message
        : '服务器内部错误'
    };
  });

  // 404处理
  fastify.setNotFoundHandler(async (request, reply) => {
    reply.code(404);
    return {
      success: false,
      error: '接口不存在',
      path: request.url
    };
  });
}

// 优雅关闭
function setupGracefulShutdown() {
  const signals = ['SIGINT', 'SIGTERM'];

  signals.forEach(signal => {
    process.on(signal, async () => {
      fastify.log.info(`收到 ${signal} 信号，开始优雅关闭...`);

      try {
        await fastify.close();
        await prisma.$disconnect();
        fastify.log.info('服务器已优雅关闭');
        process.exit(0);
      } catch (error) {
        fastify.log.error(error as any, '关闭服务器时出错');
        process.exit(1);
      }
    });
  });
}

// 启动服务器
async function start() {
  try {
    // 检查数据库连接
    const dbConnected = await checkDatabaseConnection();
    if (!dbConnected) {
      throw new Error('数据库连接失败');
    }

    // 初始化数据库
    await initializeDatabase();

    // 确保上传目录存在
    await ensureUploadDir();

    // 注册插件和路由
    await registerPlugins();
    await registerRoutes();

    // 设置错误处理
    setupErrorHandling();

    // 设置优雅关闭
    setupGracefulShutdown();

    // 启动服务器
    const port = parseInt(process.env.API_PORT || '3001');
    const host = process.env.API_HOST || 'localhost';

    await fastify.listen({ port, host });

    // Use ASCII-only log lines to avoid Windows console code page garbling
    fastify.log.info('API server started successfully');
    fastify.log.info(`Listening: http://${host}:${port}`);
    fastify.log.info(`Health:   http://${host}:${port}/api/health`);
    fastify.log.info(`Books:    http://${host}:${port}/api/books`);

  } catch (error) {
    fastify.log.error(error as any, '启动服务器失败');
    process.exit(1);
  }
}

// 启动应用
start();

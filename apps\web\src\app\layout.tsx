import type { Metadata } from 'next';
import { Inter, Noto_Serif_SC } from 'next/font/google';
import { Header } from '@/components/layout/Header';
import { Sidebar } from '@/components/layout/Sidebar';
import { AuthProvider } from '@/contexts/AuthContext';
import '@/styles/globals.css';

// 字体配置
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap',
});

const notoSerifSC = Noto_Serif_SC({
  subsets: ['latin'],
  variable: '--font-reading',
  weight: ['400', '500', '600', '700'],
  display: 'swap',
});

// 元数据配置
export const metadata: Metadata = {
  title: {
    default: '智能电子书平台',
    template: '%s | 智能电子书平台'
  },
  description: '一个现代化的Web电子书阅读平台，专注于txt格式小说的智能解析和优化阅读体验',
  keywords: ['电子书', '阅读器', 'txt', '小说', 'AI翻译', '智能解析'],
  authors: [{ name: 'Ebook Platform Team' }],
  creator: 'Ebook Platform',
  publisher: 'Ebook Platform',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: '/',
    title: '智能电子书平台',
    description: '一个现代化的Web电子书阅读平台，专注于txt格式小说的智能解析和优化阅读体验',
    siteName: '智能电子书平台',
  },
  twitter: {
    card: 'summary_large_image',
    title: '智能电子书平台',
    description: '一个现代化的Web电子书阅读平台，专注于txt格式小说的智能解析和优化阅读体验',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    // google: 'your-google-verification-code',
    // yandex: 'your-yandex-verification-code',
  },
};

// 视口配置
export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'white' },
    { media: '(prefers-color-scheme: dark)', color: 'black' },
  ],
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
      </head>
      <body
        className={`${inter.variable} ${notoSerifSC.variable} font-sans antialiased`}
        suppressHydrationWarning
      >
        <AuthProvider>
          <div className="relative flex min-h-screen">
            {/* 侧边栏 */}
            <Sidebar />

            {/* 主内容区域 */}
            <div className="flex flex-1 flex-col">
              {/* 头部导航 */}
              <Header />

              {/* 页面内容 */}
              <main className="flex-1">
                {children}
              </main>
            </div>
          </div>
        </AuthProvider>
        
        {/* 全局脚本 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // 主题初始化脚本，防止闪烁
              (function() {
                try {
                  const theme = localStorage.getItem('ebook-platform-storage');
                  if (theme) {
                    const parsed = JSON.parse(theme);
                    if (parsed.state?.theme) {
                      document.documentElement.classList.add(parsed.state.theme);
                    }
                  }
                } catch (e) {
                  console.warn('Failed to load theme from localStorage:', e);
                }
              })();
            `,
          }}
        />
      </body>
    </html>
  );
}

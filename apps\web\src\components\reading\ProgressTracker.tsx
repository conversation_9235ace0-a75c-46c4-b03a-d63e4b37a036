'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion } from 'framer-motion';
import { BookOpen, Clock, TrendingUp, Calendar } from 'lucide-react';
import { api } from '@/lib/api';
import type { ReadingProgress } from '@ebook-platform/types';

interface ProgressTrackerProps {
  bookId: string;
  currentChapter: number;
  currentPosition: number;
  totalChapters: number;
  onProgressLoad?: (progress: ReadingProgress) => void;
  className?: string;
}

export const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  bookId,
  currentChapter,
  currentPosition,
  totalChapters,
  onProgressLoad,
  className = ''
}) => {
  const [progress, setProgress] = useState<ReadingProgress | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastSaveTime, setLastSaveTime] = useState<Date | null>(null);

  // 使用 ref 来避免 onProgressLoad 导致的无限循环
  const onProgressLoadRef = useRef(onProgressLoad);
  onProgressLoadRef.current = onProgressLoad;

  // 计算阅读百分比
  const calculatePercentage = useCallback((chapter: number, position: number) => {
    if (totalChapters === 0) return 0;
    const chapterProgress = chapter / totalChapters;
    const positionProgress = position / 10000; // 假设每章最多10000个位置
    return Math.min(Math.round((chapterProgress + positionProgress / totalChapters) * 100), 100);
  }, [totalChapters]);

  // 获取阅读进度
  const fetchProgress = useCallback(async () => {
    try {
      setLoading(true);
      const response = await api.progress.get(bookId);
      if (response.success) {
        const progressData = response.data;
        setProgress(progressData);
        if (onProgressLoadRef.current && progressData) {
          onProgressLoadRef.current(progressData);
        }
      }
    } catch (err) {
      console.error('获取阅读进度失败:', err);
    } finally {
      setLoading(false);
    }
  }, [bookId]);

  // 保存阅读进度
  const saveProgress = useCallback(async (chapter: number, position: number) => {
    try {
      const percentage = calculatePercentage(chapter, position);
      const response = await api.progress.update(bookId, {
        currentChapter: chapter,
        currentPosition: position,
        percentage
      });
      
      if (response.success) {
        setProgress(response.data);
        setLastSaveTime(new Date());
      }
    } catch (err) {
      console.error('保存阅读进度失败:', err);
    }
  }, [bookId, calculatePercentage]);

  // 防抖保存进度
  const [saveTimeout, setSaveTimeout] = useState<NodeJS.Timeout | null>(null);
  
  const debouncedSaveProgress = useCallback((chapter: number, position: number) => {
    setSaveTimeout(prevTimeout => {
      if (prevTimeout) {
        clearTimeout(prevTimeout);
      }

      return setTimeout(() => {
        saveProgress(chapter, position);
      }, 2000); // 2秒后保存
    });
  }, [saveProgress]);

  // 初始化时获取进度
  useEffect(() => {
    fetchProgress();
  }, [fetchProgress]);

  // 当前位置变化时自动保存
  useEffect(() => {
    if (!loading && progress) {
      // 只有当位置真正变化时才保存
      if (currentChapter !== progress.currentChapter ||
          Math.abs(currentPosition - progress.currentPosition) > 10) {
        debouncedSaveProgress(currentChapter, currentPosition);
      }
    }
  }, [currentChapter, currentPosition, progress, loading]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (saveTimeout) {
        clearTimeout(saveTimeout);
      }
    };
  }, [saveTimeout]);

  // 格式化时间
  const formatTime = (date: Date | string) => {
    const d = new Date(date);
    return d.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 计算当前百分比
  const currentPercentage = calculatePercentage(currentChapter, currentPosition);

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded mb-2"></div>
          <div className="h-2 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white rounded-lg shadow-sm border ${className}`}
    >
      <div className="p-4">
        {/* 标题 */}
        <div className="flex items-center gap-2 mb-4">
          <TrendingUp className="h-5 w-5 text-indigo-600" />
          <h3 className="font-semibold text-gray-900">阅读进度</h3>
        </div>

        {/* 进度条 */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600">
              第 {currentChapter + 1} 章 / 共 {totalChapters} 章
            </span>
            <span className="text-sm font-medium text-indigo-600">
              {currentPercentage}%
            </span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              className="bg-indigo-600 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${currentPercentage}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </div>

        {/* 统计信息 */}
        <div className="flex justify-center text-sm">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <div>
              <div className="text-gray-600">上次阅读</div>
              <div className="font-medium">
                {progress?.lastReadAt ? formatTime(progress.lastReadAt) : '未知'}
              </div>
            </div>
          </div>
        </div>

        {/* 保存状态 */}
        {lastSaveTime && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="mt-3 pt-3 border-t border-gray-100"
          >
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <Clock className="h-3 w-3" />
              <span>已保存 - {formatTime(lastSaveTime)}</span>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

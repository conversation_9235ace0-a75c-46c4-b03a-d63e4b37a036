'use client';

import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion, AnimatePresence } from 'framer-motion';
import { Upload, File, X, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { api, handleApiError } from '@/lib/api';
import { formatFileSize } from '@/lib/utils';
import type { Book } from '@ebook-platform/types';

interface FileUploadProps {
  onUploadSuccess?: (book: Book) => void;
  onUploadError?: (error: string) => void;
  className?: string;
}

interface UploadingFile {
  file: File;
  progress: number;
  status: 'uploading' | 'processing' | 'success' | 'error';
  error?: string;
  bookId?: string;
  book?: Book;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onUploadSuccess,
  onUploadError,
  className = ''
}) => {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);

  // 使用useCallback包装回调函数，避免渲染期间调用
  const handleUploadSuccess = useCallback((book: Book) => {
    setTimeout(() => onUploadSuccess?.(book), 0);
  }, [onUploadSuccess]);

  const handleUploadError = useCallback((error: string) => {
    setTimeout(() => onUploadError?.(error), 0);
  }, [onUploadError]);

  // 文件验证
  const validateFile = (file: File): string | null => {
    // 检查文件类型
    if (!file.name.toLowerCase().endsWith('.txt')) {
      return '只支持 .txt 格式的文件';
    }
    
    // 检查文件大小 (50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return `文件大小不能超过 ${formatFileSize(maxSize)}`;
    }
    
    // 检查文件是否为空
    if (file.size === 0) {
      return '文件不能为空';
    }
    
    return null;
  };

  // 上传文件
  const uploadFile = async (file: File) => {
    const uploadingFile: UploadingFile = {
      file,
      progress: 0,
      status: 'uploading'
    };

    setUploadingFiles(prev => [...prev, uploadingFile]);

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadingFiles(prev => 
          prev.map(f => 
            f.file === file && f.progress < 90 
              ? { ...f, progress: f.progress + 10 }
              : f
          )
        );
      }, 200);

      // 调用上传API
      const response = await api.books.upload(file);

      clearInterval(progressInterval);

      if (response.success && response.data) {
        // 上传成功，开始处理
        setUploadingFiles(prev => 
          prev.map(f => 
            f.file === file 
              ? { 
                  ...f, 
                  progress: 100, 
                  status: 'processing',
                  bookId: response.data.id,
                  book: response.data
                }
              : f
          )
        );

        // 轮询处理进度
        await pollProcessingProgress(file, response.data.id);
        
      } else {
        throw new Error(response.error || '上传失败');
      }

    } catch (error) {
      const errorMessage = handleApiError(error);
      
      setUploadingFiles(prev => 
        prev.map(f => 
          f.file === file 
            ? { ...f, status: 'error', error: errorMessage }
            : f
        )
      );

      handleUploadError(errorMessage);
    }
  };

  // 轮询处理进度
  const pollProcessingProgress = async (file: File, bookId: string) => {
    const maxAttempts = 30; // 最多轮询30次 (30秒)
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;

        // 调用后端进度查询API
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api'}/upload/${bookId}/progress`);
        const data = await response.json();

        if (data.success && data.data) {
          const { status, progress } = data.data;

          if (status === 'ready') {
            // 处理完成
            setUploadingFiles(prev =>
              prev.map(f => {
                if (f.file === file) {
                  const updatedFile = { ...f, status: 'success' as const };
                  if (f.book) {
                    // 更新书籍信息
                    const updatedBook = {
                      ...f.book,
                      status: 'ready' as any,
                      chapterCount: data.data.chapterCount || 0,
                      totalWords: data.data.totalWords || 0
                    };
                    handleUploadSuccess(updatedBook);
                  }
                  return updatedFile;
                }
                return f;
              })
            );
            return;
          } else if (status === 'error') {
            // 处理失败
            setUploadingFiles(prev =>
              prev.map(f =>
                f.file === file
                  ? { ...f, status: 'error', error: '文件处理失败' }
                  : f
              )
            );
            return;
          }
        }

        // 继续轮询
        if (attempts < maxAttempts) {
          setTimeout(poll, 1000);
        } else {
          // 超时
          setUploadingFiles(prev =>
            prev.map(f =>
              f.file === file
                ? { ...f, status: 'error', error: '处理超时' }
                : f
            )
          );
        }

      } catch (error) {
        console.error('轮询进度失败:', error);

        // 继续轮询，除非达到最大次数
        if (attempts < maxAttempts) {
          setTimeout(poll, 2000); // 出错时延长间隔
        } else {
          setUploadingFiles(prev =>
            prev.map(f =>
              f.file === file
                ? { ...f, status: 'error', error: '无法获取处理状态' }
                : f
            )
          );
        }
      }
    };

    // 延迟开始轮询，给后端处理时间
    setTimeout(poll, 1000);
  };

  // 处理文件拖拽
  const onDrop = useCallback((acceptedFiles: File[]) => {
    acceptedFiles.forEach(file => {
      const error = validateFile(file);
      if (error) {
        handleUploadError(error);
        return;
      }

      uploadFile(file);
    });
  }, [handleUploadError]);

  // 配置dropzone
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.txt']
    },
    multiple: true,
    maxSize: 50 * 1024 * 1024 // 50MB
  });

  // 移除上传项
  const removeUploadItem = (file: File) => {
    setUploadingFiles(prev => prev.filter(f => f.file !== file));
  };

  // 获取状态图标
  const getStatusIcon = (status: UploadingFile['status']) => {
    switch (status) {
      case 'uploading':
      case 'processing':
        return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
    }
  };

  // 获取状态文本
  const getStatusText = (uploadingFile: UploadingFile) => {
    switch (uploadingFile.status) {
      case 'uploading':
        return `上传中... ${uploadingFile.progress}%`;
      case 'processing':
        return '解析中...';
      case 'success':
        return '上传成功';
      case 'error':
        return uploadingFile.error || '上传失败';
    }
  };

  return (
    <div className={className}>
      {/* 拖拽上传区域 */}
      <motion.div
        {...getRootProps()}
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer
          transition-colors duration-200
          ${isDragActive 
            ? 'border-primary bg-primary/5' 
            : 'border-muted-foreground/25 hover:border-primary/50 hover:bg-muted/50'
          }
        `}
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
      >
        <input {...getInputProps()} />
        
        <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
        
        <h3 className="text-lg font-semibold mb-2">
          {isDragActive ? '释放文件开始上传' : '上传txt电子书'}
        </h3>
        
        <p className="text-muted-foreground mb-4">
          拖拽文件到此处，或点击选择文件
        </p>
        
        <div className="text-sm text-muted-foreground">
          <p>支持格式：.txt</p>
          <p>最大大小：50MB</p>
        </div>
      </motion.div>

      {/* 上传列表 */}
      <AnimatePresence>
        {uploadingFiles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-6 space-y-3"
          >
            {uploadingFiles.map((uploadingFile, index) => (
              <motion.div
                key={`${uploadingFile.file.name}-${index}`}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                className="flex items-center space-x-3 p-4 bg-card rounded-lg border"
              >
                <File className="h-8 w-8 text-muted-foreground flex-shrink-0" />
                
                <div className="flex-1 min-w-0">
                  <p className="font-medium truncate">{uploadingFile.file.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {formatFileSize(uploadingFile.file.size)}
                  </p>
                  
                  {/* 进度条 */}
                  {uploadingFile.status === 'uploading' && (
                    <div className="mt-2">
                      <div className="w-full bg-muted rounded-full h-2">
                        <motion.div
                          className="bg-primary h-2 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${uploadingFile.progress}%` }}
                          transition={{ duration: 0.3 }}
                        />
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center space-x-2">
                  {getStatusIcon(uploadingFile.status)}
                  <span className="text-sm text-muted-foreground">
                    {getStatusText(uploadingFile)}
                  </span>
                  
                  <button
                    onClick={() => removeUploadItem(uploadingFile.file)}
                    className="p-1 hover:bg-muted rounded"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

import { AuthResponse, RegisterRequest, LoginRequest, User } from '@/types/auth';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

/**
 * 用户注册
 */
export async function register(data: RegisterRequest): Promise<AuthResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        'Accept': 'application/json; charset=utf-8',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();
    
    if (!response.ok) {
      return {
        success: false,
        error: result.error || 'REGISTRATION_FAILED',
        message: result.message || '注册失败，请稍后重试'
      };
    }

    return result;
  } catch (error) {
    console.error('注册请求失败:', error);
    return {
      success: false,
      error: 'NETWORK_ERROR',
      message: '网络错误，请检查网络连接'
    };
  }
}

/**
 * 用户登录
 */
export async function login(data: LoginRequest): Promise<AuthResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        'Accept': 'application/json; charset=utf-8',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();
    
    if (!response.ok) {
      return {
        success: false,
        error: result.error || 'LOGIN_FAILED',
        message: result.message || '登录失败，请稍后重试'
      };
    }

    return result;
  } catch (error) {
    console.error('登录请求失败:', error);
    return {
      success: false,
      error: 'NETWORK_ERROR',
      message: '网络错误，请检查网络连接'
    };
  }
}

/**
 * 获取当前用户信息
 */
export async function getCurrentUser(token: string): Promise<{ success: boolean; user?: User; error?: string }> {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json; charset=utf-8',
      },
    });

    const result = await response.json();
    
    if (!response.ok) {
      return {
        success: false,
        error: result.error || 'GET_USER_FAILED'
      };
    }

    return {
      success: true,
      user: result.data.user
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      success: false,
      error: 'NETWORK_ERROR'
    };
  }
}

/**
 * 用户登出
 */
export async function logout(token: string): Promise<{ success: boolean }> {
  try {
    await fetch(`${API_BASE_URL}/auth/logout`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json; charset=utf-8',
      },
    });

    return { success: true };
  } catch (error) {
    console.error('登出请求失败:', error);
    // 即使请求失败，也认为登出成功（清除本地状态）
    return { success: true };
  }
}

/**
 * 验证token是否有效
 */
export async function validateToken(token: string): Promise<boolean> {
  try {
    const result = await getCurrentUser(token);
    return result.success;
  } catch (error) {
    return false;
  }
}

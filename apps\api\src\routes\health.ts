import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { checkDatabaseConnection } from '../lib/database.js';
import { caches, cacheUtils } from '../lib/cache.js';

// 健康检查路由
export async function healthRoutes(fastify: FastifyInstance) {
  // 基础健康检查
  fastify.get('/health', async (request: FastifyRequest, reply: FastifyReply) => {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '0.1.0'
    };
  });
  
  // 详细健康检查
  fastify.get('/health/detailed', async (request: FastifyRequest, reply: FastifyReply) => {
    const checks = {
      database: false,
      cache: false,
      memory: false,
      disk: false
    };
    
    try {
      // 数据库连接检查
      checks.database = await checkDatabaseConnection();
      
      // 缓存检查
      try {
        cacheUtils.set(caches.session, 'health_check', 'ok', 10);
        const result = cacheUtils.get(caches.session, 'health_check');
        checks.cache = result === 'ok';
        cacheUtils.del(caches.session, 'health_check');
      } catch {
        checks.cache = false;
      }
      
      // 内存使用检查
      const memUsage = process.memoryUsage();
      checks.memory = memUsage.heapUsed < memUsage.heapTotal * 0.9; // 90%阈值
      
      // 磁盘空间检查（简单检查）
      checks.disk = true; // 暂时总是返回true，后续可以添加实际检查
      
      const allHealthy = Object.values(checks).every(check => check);
      
      return {
        status: allHealthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        checks,
        system: {
          uptime: process.uptime(),
          memory: {
            used: Math.round(memUsage.heapUsed / 1024 / 1024),
            total: Math.round(memUsage.heapTotal / 1024 / 1024),
            external: Math.round(memUsage.external / 1024 / 1024)
          },
          cache: {
            books: cacheUtils.getStats(caches.books),
            chapters: cacheUtils.getStats(caches.chapters),
            ai: cacheUtils.getStats(caches.ai),
            translation: cacheUtils.getStats(caches.translation)
          }
        }
      };
    } catch (error) {
      reply.code(500);
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : '未知错误',
        checks
      };
    }
  });
  
  // 缓存统计
  fastify.get('/health/cache', async (request: FastifyRequest, reply: FastifyReply) => {
    return {
      timestamp: new Date().toISOString(),
      caches: {
        books: cacheUtils.getStats(caches.books),
        chapters: cacheUtils.getStats(caches.chapters),
        ai: cacheUtils.getStats(caches.ai),
        translation: cacheUtils.getStats(caches.translation),
        session: cacheUtils.getStats(caches.session)
      }
    };
  });
}

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { prisma } from '../lib/database.js';
import { 
  hashPassword, 
  verifyPassword, 
  generateToken, 
  validateUsername, 
  validateEmail, 
  validatePassword,
  JWTPayload
} from '../lib/auth.js';
import { authenticateToken } from '../middleware/auth.js';

// 注册请求接口
interface RegisterRequest {
  Body: {
    username: string;
    email: string;
    password: string;
    displayName?: string;
  };
}

// 登录请求接口
interface LoginRequest {
  Body: {
    username: string;
    password: string;
  };
}

export async function authRoutes(fastify: FastifyInstance) {
  
  /**
   * 用户注册
   */
  fastify.post('/auth/register', async (request: FastifyRequest<RegisterRequest>, reply: FastifyReply) => {
    try {
      const { username, email, password, displayName } = request.body;
      
      // 验证输入
      const usernameValidation = validateUsername(username);
      if (!usernameValidation.valid) {
        reply.code(400);
        return { success: false, error: 'INVALID_USERNAME', message: usernameValidation.message };
      }
      
      const emailValidation = validateEmail(email);
      if (!emailValidation.valid) {
        reply.code(400);
        return { success: false, error: 'INVALID_EMAIL', message: emailValidation.message };
      }
      
      const passwordValidation = validatePassword(password);
      if (!passwordValidation.valid) {
        reply.code(400);
        return { success: false, error: 'INVALID_PASSWORD', message: passwordValidation.message };
      }
      
      // 检查用户名是否已存在
      const existingUserByUsername = await prisma.user.findUnique({
        where: { username }
      });
      
      if (existingUserByUsername) {
        reply.code(409);
        return { success: false, error: 'USERNAME_EXISTS', message: '用户名已存在' };
      }
      
      // 检查邮箱是否已存在
      const existingUserByEmail = await prisma.user.findUnique({
        where: { email }
      });
      
      if (existingUserByEmail) {
        reply.code(409);
        return { success: false, error: 'EMAIL_EXISTS', message: '邮箱已被注册' };
      }
      
      // 哈希密码
      const passwordHash = await hashPassword(password);
      
      // 创建用户
      const user = await prisma.user.create({
        data: {
          username,
          email,
          passwordHash,
          displayName: displayName || username
        },
        select: {
          id: true,
          username: true,
          email: true,
          displayName: true,
          createdAt: true
        }
      });
      
      // 生成JWT token
      const tokenPayload: JWTPayload = {
        userId: user.id,
        username: user.username,
        email: user.email
      };
      
      const token = generateToken(tokenPayload);
      
      console.log(`✅ 新用户注册成功: ${username} (${email})`);
      
      return {
        success: true,
        message: '注册成功',
        data: {
          user,
          token
        }
      };
      
    } catch (error) {
      console.error('用户注册失败:', error);
      reply.code(500);
      return { success: false, error: 'REGISTRATION_FAILED', message: '注册失败，请稍后重试' };
    }
  });
  
  /**
   * 用户登录
   */
  fastify.post('/auth/login', async (request: FastifyRequest<LoginRequest>, reply: FastifyReply) => {
    try {
      const { username, password } = request.body;
      
      if (!username || !password) {
        reply.code(400);
        return { success: false, error: 'MISSING_CREDENTIALS', message: '用户名和密码不能为空' };
      }
      
      // 查找用户（支持用户名或邮箱登录）
      const user = await prisma.user.findFirst({
        where: {
          OR: [
            { username },
            { email: username }
          ],
          isActive: true
        }
      });
      
      if (!user) {
        reply.code(401);
        return { success: false, error: 'INVALID_CREDENTIALS', message: '用户名或密码错误' };
      }
      
      // 验证密码
      const isPasswordValid = await verifyPassword(password, user.passwordHash);
      
      if (!isPasswordValid) {
        reply.code(401);
        return { success: false, error: 'INVALID_CREDENTIALS', message: '用户名或密码错误' };
      }
      
      // 生成JWT token
      const tokenPayload: JWTPayload = {
        userId: user.id,
        username: user.username,
        email: user.email
      };
      
      const token = generateToken(tokenPayload);
      
      console.log(`✅ 用户登录成功: ${user.username}`);
      
      return {
        success: true,
        message: '登录成功',
        data: {
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            displayName: user.displayName,
            createdAt: user.createdAt
          },
          token
        }
      };
      
    } catch (error) {
      console.error('用户登录失败:', error);
      reply.code(500);
      return { success: false, error: 'LOGIN_FAILED', message: '登录失败，请稍后重试' };
    }
  });
  
  /**
   * 获取当前用户信息
   */
  fastify.get('/auth/me', { preHandler: authenticateToken }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userId = request.user!.userId;
      
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          username: true,
          email: true,
          displayName: true,
          avatar: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              books: true,
              bookmarks: true
            }
          }
        }
      });
      
      if (!user) {
        reply.code(404);
        return { success: false, error: 'USER_NOT_FOUND', message: '用户不存在' };
      }
      
      return {
        success: true,
        data: {
          user
        }
      };
      
    } catch (error) {
      console.error('获取用户信息失败:', error);
      reply.code(500);
      return { success: false, error: 'GET_USER_FAILED', message: '获取用户信息失败' };
    }
  });
  
  /**
   * 用户登出（客户端处理，服务端记录日志）
   */
  fastify.post('/auth/logout', { preHandler: authenticateToken }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const username = request.user!.username;
      console.log(`📤 用户登出: ${username}`);
      
      return {
        success: true,
        message: '登出成功'
      };
      
    } catch (error) {
      console.error('用户登出失败:', error);
      reply.code(500);
      return { success: false, error: 'LOGOUT_FAILED', message: '登出失败' };
    }
  });
  
}

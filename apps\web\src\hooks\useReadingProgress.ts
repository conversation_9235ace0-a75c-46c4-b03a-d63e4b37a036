'use client';

import { useState, useEffect, useCallback } from 'react';
import { api } from '@/lib/api';
import type { ReadingProgress } from '@ebook-platform/types';

interface UseReadingProgressOptions {
  bookId: string;
  autoSave?: boolean;
  saveDelay?: number;
}

interface UseReadingProgressReturn {
  progress: ReadingProgress | null;
  loading: boolean;
  error: string | null;
  updateProgress: (chapter: number, position: number) => void;
  saveProgress: (chapter: number, position: number) => Promise<void>;
  refreshProgress: () => Promise<void>;
  lastSaveTime: Date | null;
}

export const useReadingProgress = ({
  bookId,
  autoSave = true,
  saveDelay = 2000
}: UseReadingProgressOptions): UseReadingProgressReturn => {
  const [progress, setProgress] = useState<ReadingProgress | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastSaveTime, setLastSaveTime] = useState<Date | null>(null);
  const [saveTimeout, setSaveTimeout] = useState<NodeJS.Timeout | null>(null);

  // 计算阅读百分比
  const calculatePercentage = useCallback((chapter: number, position: number, totalChapters: number = 1) => {
    if (totalChapters === 0) return 0;
    const chapterProgress = chapter / totalChapters;
    const positionProgress = position / 10000; // 假设每章最多10000个位置
    return Math.min(Math.round((chapterProgress + positionProgress / totalChapters) * 100), 100);
  }, []);

  // 获取阅读进度
  const fetchProgress = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await api.progress.get(bookId);
      
      if (response.success) {
        setProgress(response.data);
      } else {
        setError(response.message || '获取阅读进度失败');
      }
    } catch (err) {
      setError('网络错误，请稍后重试');
      console.error('获取阅读进度失败:', err);
    } finally {
      setLoading(false);
    }
  }, [bookId]);

  // 保存阅读进度
  const saveProgress = useCallback(async (chapter: number, position: number) => {
    try {
      const percentage = calculatePercentage(chapter, position);
      const response = await api.progress.update(bookId, {
        currentChapter: chapter,
        currentPosition: position,
        percentage
      });
      
      if (response.success) {
        setProgress(response.data);
        setLastSaveTime(new Date());
        setError(null);
      } else {
        setError(response.message || '保存阅读进度失败');
      }
    } catch (err) {
      setError('保存失败，请检查网络连接');
      console.error('保存阅读进度失败:', err);
    }
  }, [bookId, calculatePercentage]);

  // 防抖更新进度
  const updateProgress = useCallback((chapter: number, position: number) => {
    if (!autoSave) return;

    // 清除之前的定时器
    if (saveTimeout) {
      clearTimeout(saveTimeout);
    }

    // 设置新的定时器
    const timeout = setTimeout(() => {
      saveProgress(chapter, position);
    }, saveDelay);

    setSaveTimeout(timeout);
  }, [autoSave, saveDelay, saveProgress, saveTimeout]);

  // 刷新进度
  const refreshProgress = useCallback(async () => {
    await fetchProgress();
  }, [fetchProgress]);

  // 初始化时获取进度
  useEffect(() => {
    fetchProgress();
  }, [fetchProgress]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (saveTimeout) {
        clearTimeout(saveTimeout);
      }
    };
  }, [saveTimeout]);

  return {
    progress,
    loading,
    error,
    updateProgress,
    saveProgress,
    refreshProgress,
    lastSaveTime
  };
};

'use client';

import React from 'react';
import { X, Home, BookOpen, Upload, Bookmark, Settings, TrendingUp } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppActions, useSidebarOpen, useBooks } from '@/store';
import { cn } from '@/lib/utils';

interface SidebarProps {
  className?: string;
}

export const Sidebar: React.FC<SidebarProps> = ({ className }) => {
  const sidebarOpen = useSidebarOpen();
  const books = useBooks();
  const { setSidebarOpen } = useAppActions();

  const menuItems = [
    { icon: Home, label: '首页', href: '/', count: null },
    { icon: BookOpen, label: '我的书库', href: '/books', count: books.length },
    { icon: Upload, label: '上传书籍', href: '/upload', count: null },
    { icon: Bookmark, label: '书签收藏', href: '/bookmarks', count: null },
    { icon: TrendingUp, label: '阅读统计', href: '/stats', count: null },
    { icon: Settings, label: '设置', href: '/settings', count: null },
  ];

  return (
    <>
      {/* 遮罩层 */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSidebarOpen(false)}
            className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          />
        )}
      </AnimatePresence>

      {/* 侧边栏 */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.aside
            initial={{ x: -300 }}
            animate={{ x: 0 }}
            exit={{ x: -300 }}
            transition={{ type: 'spring', damping: 30, stiffness: 300 }}
            className={cn(
              'fixed left-0 top-0 z-50 h-full w-64 bg-card border-r shadow-lg lg:relative lg:translate-x-0',
              className
            )}
          >
            {/* 侧边栏头部 */}
            <div className="flex h-14 items-center justify-between border-b px-4">
              <h2 className="text-lg font-semibold">菜单</h2>
              <button
                onClick={() => setSidebarOpen(false)}
                className="inline-flex items-center justify-center rounded-md p-2 text-muted-foreground hover:bg-accent hover:text-accent-foreground lg:hidden"
                aria-label="关闭菜单"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* 导航菜单 */}
            <nav className="flex-1 space-y-1 p-4">
              {menuItems.map((item) => (
                <button
                  key={item.href}
                  onClick={() => {
                    setSidebarOpen(false);
                    window.location.href = item.href;
                  }}
                  className="w-full flex items-center justify-between rounded-lg px-3 py-2 text-sm font-medium text-muted-foreground transition-colors hover:bg-accent hover:text-accent-foreground"
                >
                  <div className="flex items-center space-x-3">
                    <item.icon className="h-5 w-5" />
                    <span>{item.label}</span>
                  </div>
                  {item.count !== null && (
                    <span className="rounded-full bg-primary/10 px-2 py-1 text-xs text-primary">
                      {item.count}
                    </span>
                  )}
                </button>
              ))}
            </nav>

            {/* 侧边栏底部 */}
            <div className="border-t p-4">
              <div className="text-xs text-muted-foreground">
                <p>智能电子书平台</p>
                <p className="mt-1">版本 0.1.0</p>
              </div>
            </div>
          </motion.aside>
        )}
      </AnimatePresence>
    </>
  );
};

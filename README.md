# 📚 智能电子书阅读平台

一个现代化的Web电子书阅读平台，专注于txt格式小说的智能解析和优化阅读体验。

## ✨ 核心功能

- 📖 **智能文本解析** - 自动识别章节结构，生成目录导航
- 🎨 **沉浸式阅读** - 个性化阅读设置，支持多种主题和字体
- 🔖 **智能书签** - 高级书签系统，支持笔记和分类管理
- 🌐 **沉浸式翻译** - 基于AI的双语对照翻译功能
- 🤖 **AI阅读助手** - 章节摘要、人物关系图谱等智能功能
- 📱 **响应式设计** - 完美适配桌面端、平板和移动设备

## 🛠️ 技术栈

### 前端
- **框架**: Next.js 14 + TypeScript
- **样式**: Tailwind CSS + Shadcn/ui
- **动画**: Framer Motion
- **状态管理**: Zustand

### 后端
- **框架**: Fastify + TypeScript
- **数据库**: SQLite + Prisma (可升级至PostgreSQL)
- **文件存储**: 本地文件系统
- **缓存**: 内存缓存 (node-cache)

### 开发工具
- **包管理**: pnpm
- **构建工具**: Turbo (Monorepo)
- **代码规范**: ESLint + Prettier
- **类型检查**: TypeScript

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- pnpm >= 8.0.0

### 安装依赖
```bash
# 克隆项目
git clone <repository-url>
cd ebook-platform

# 安装依赖
pnpm install

# 复制环境变量
cp .env.example .env.local
```

### 开发环境
```bash
# 启动开发服务器
pnpm dev

# 前端: http://localhost:3000
# 后端API: http://localhost:3001
```

### 构建生产版本
```bash
# 构建所有应用
pnpm build

# 运行生产版本
pnpm start
```

## 📁 项目结构

```
ebook-platform/
├── apps/
│   ├── web/                 # Next.js前端应用
│   └── api/                 # Fastify后端API
├── packages/
│   ├── ui/                  # 共享UI组件
│   ├── types/               # TypeScript类型定义
│   └── utils/               # 工具函数库
├── data/
│   ├── database.sqlite      # SQLite数据库
│   └── uploads/             # 文件上传目录
└── docs/                    # 项目文档
```

## 🎯 开发路线图

- [x] 项目脚手架搭建
- [ ] 后端API基础架构
- [ ] 前端基础框架
- [ ] 文件上传功能
- [ ] 文本解析引擎
- [ ] 基础阅读界面
- [ ] 数据持久化
- [ ] Markdown转换功能
- [ ] 阅读体验优化
- [ ] 智能书签系统
- [ ] 翻译功能集成
- [ ] AI阅读助手
- [ ] 性能优化与测试
- [ ] 部署与文档

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

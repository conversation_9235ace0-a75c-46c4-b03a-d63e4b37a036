// 测试当前配置的OpenAI API
const axios = require('axios');
const dotenv = require('dotenv');
const path = require('path');

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '.env.local') });

const API_KEY = process.env.OPENAI_API_KEY;
const BASE_URL = process.env.OPENAI_BASE_URL;
const MODEL = process.env.OPENAI_MODEL;
const API_URL = `${BASE_URL}/v1/chat/completions`;

console.log('🧪 测试当前配置的OpenAI API...');
console.log(`📡 API地址: ${API_URL}`);
console.log(`🔑 API密钥: ${API_KEY ? API_KEY.substring(0, 10) + '...' : '未设置'}`);
console.log(`🤖 模型: ${MODEL}`);

async function testCurrentConfig() {
  try {
    const response = await axios.post(API_URL, {
      model: MODEL,
      messages: [
        {
          role: 'user',
          content: '请回复"测试成功"'
        }
      ],
      max_tokens: 50,
      temperature: 0.1
    }, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      },
      timeout: 30000
    });

    console.log('✅ API测试成功!');
    console.log(`📊 状态码: ${response.status}`);
    console.log(`🤖 AI回复: ${response.data.choices[0].message.content}`);
    
    return response.data;
  } catch (error) {
    console.error('❌ API测试失败:', error.message);
    
    if (error.response) {
      console.error(`📊 响应状态: ${error.response.status}`);
      console.error(`📝 响应数据:`, error.response.data);
    } else if (error.request) {
      console.error('📤 请求发送失败:', error.code);
    }
    
    throw error;
  }
}

testCurrentConfig().catch(console.error);

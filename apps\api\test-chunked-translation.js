// 测试分片翻译功能
const axios = require('axios');
const dotenv = require('dotenv');
const path = require('path');

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '.env.local') });

const API_URL = 'http://localhost:3001/api';

// 模拟长文本
const longText = `
这是一个很长的文本，用来测试分片翻译功能。在现代软件开发中，翻译功能是国际化应用的重要组成部分。
当处理长文本时，我们需要考虑API的限制和性能问题。分片翻译是一种有效的解决方案，它将长文本分割成较小的片段，
分别进行翻译，然后将结果合并。这种方法不仅可以避免API的长度限制，还能提高翻译的准确性和一致性。

在实现分片翻译时，我们需要考虑以下几个关键因素：
1. 合理的分片策略：按段落、句子或语义单元进行分割
2. 上下文保持：为每个片段提供必要的上下文信息
3. 错误处理：处理单个片段翻译失败的情况
4. 性能优化：控制并发请求数量和延迟
5. 结果合并：保持原文的格式和结构

通过智能分片翻译，我们可以处理各种长度的文本，从短句到长篇文章，都能获得高质量的翻译结果。
这对于电子书阅读应用来说特别重要，因为用户可能需要翻译整个章节或段落。

现代AI翻译技术的发展使得这种分片策略更加可行。大型语言模型具有强大的上下文理解能力，
即使在分片的情况下，也能保持翻译的连贯性和准确性。同时，通过合理的提示工程，
我们可以指导AI在翻译时保持一致的风格和术语使用。

总的来说，分片翻译是处理长文本翻译的最佳实践之一，它平衡了性能、准确性和用户体验。
`.trim();

console.log('🧪 测试分片翻译功能...');
console.log(`📝 测试文本长度: ${longText.length} 字符`);

async function testChunkedTranslation() {
  try {
    // 首先尝试注册测试用户
    try {
      await axios.post(`${API_URL}/auth/register`, {
        username: 'test',
        password: 'test123',
        email: '<EMAIL>'
      });
      console.log('✅ 测试用户注册成功');
    } catch (regError) {
      console.log('ℹ️ 测试用户可能已存在');
    }

    // 获取认证token
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      username: 'test',
      password: 'test123'
    });

    if (!loginResponse.data.success) {
      console.error('❌ 登录失败:', loginResponse.data);
      return;
    }

    const token = loginResponse.data.data.token;
    console.log('✅ 登录成功');

    // 测试标准翻译API（应该失败，因为文本过长）
    console.log('\n📝 测试标准翻译API...');
    try {
      const standardResponse = await axios.post(`${API_URL}/translate`, {
        text: longText,
        sourceLanguage: 'zh-CN',
        targetLanguage: 'en'
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      });

      console.log('📊 标准翻译结果:', standardResponse.data);
    } catch (error) {
      console.log('❌ 标准翻译失败（预期）:', error.response?.data?.error || error.message);
    }

    // 测试批量翻译API
    console.log('\n📄 测试批量翻译API...');
    const startTime = Date.now();
    
    const batchResponse = await axios.post(`${API_URL}/translate/batch`, {
      text: longText,
      sourceLanguage: 'zh-CN',
      targetLanguage: 'en'
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 120000 // 2分钟超时
    });

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    if (batchResponse.data.success) {
      console.log('✅ 批量翻译成功!');
      console.log(`⏱️ 翻译耗时: ${duration.toFixed(2)} 秒`);
      console.log(`📊 原文长度: ${longText.length} 字符`);
      console.log(`📊 译文长度: ${batchResponse.data.data.translatedText.length} 字符`);
      console.log(`🔄 翻译方法: ${batchResponse.data.data.method}`);
      console.log('\n📝 翻译结果预览:');
      console.log(batchResponse.data.data.translatedText.substring(0, 200) + '...');
    } else {
      console.error('❌ 批量翻译失败:', batchResponse.data.error);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

testChunkedTranslation().catch(console.error);

// 使用axios测试OpenAI API
const axios = require('axios');

const API_KEY = 'sk-nVKlm9f0TwJ5mJinzv4fWD7JN8OidrHGHHWugycD68wVFThy';
const API_URL = 'https://for.shuo.bar/v1/chat/completions';

console.log('🧪 开始axios OpenAI API测试...');
console.log(`📡 API地址: ${API_URL}`);

async function testOpenAI() {
  try {
    const response = await axios.post(API_URL, {
      model: 'gpt-4o',
      messages: [
        {
          role: 'user',
          content: '请回复"测试成功"'
        }
      ],
      max_tokens: 50,
      temperature: 0.1
    }, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      },
      timeout: 30000
    });

    console.log('✅ API测试成功!');
    console.log(`📊 状态码: ${response.status}`);
    console.log(`🤖 AI回复: ${response.data.choices[0].message.content}`);
    console.log(`📝 完整响应:`, JSON.stringify(response.data, null, 2));
    
    return response.data;

  } catch (error) {
    console.error('❌ API测试失败:', error.message);
    
    if (error.response) {
      console.error('📊 响应状态:', error.response.status);
      console.error('📋 响应头:', error.response.headers);
      console.error('📝 响应内容:', error.response.data);
    } else if (error.request) {
      console.error('📤 请求发送失败:', error.request);
    } else {
      console.error('⚙️ 配置错误:', error.message);
    }
    
    throw error;
  }
}

// 运行测试
testOpenAI()
  .then(() => {
    console.log('🎉 测试完成!');
    process.exit(0);
  })
  .catch(() => {
    console.log('💥 测试失败!');
    process.exit(1);
  });

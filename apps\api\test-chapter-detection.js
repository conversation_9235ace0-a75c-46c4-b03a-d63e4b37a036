// 测试章节检测功能
const fs = require('fs');
const path = require('path');

// 复制detectChapters函数
const detectChapters = (content) => {
  const chapterPatterns = [
    /^第[一二三四五六七八九十百千万\d]+章\s*.*/gm,
    /^第[一二三四五六七八九十百千万\d]+节\s*.*/gm,
    /^Chapter\s+\d+.*/gim,
    /^\d+\.\s*.*/gm,
    /^[一二三四五六七八九十百千万]+、.*/gm
  ];
  
  const chapters = [];
  
  console.log('🔍 开始测试章节检测...');
  console.log('📝 文本内容:');
  console.log(content);
  console.log('\n' + '='.repeat(50) + '\n');
  
  for (let i = 0; i < chapterPatterns.length; i++) {
    const pattern = chapterPatterns[i];
    console.log(`🧪 测试模式 ${i + 1}: ${pattern}`);
    
    const matches = Array.from(content.matchAll(pattern));
    console.log(`📊 匹配结果: ${matches.length} 个`);
    
    if (matches.length > 0) {
      matches.forEach((match, index) => {
        console.log(`  ${index + 1}. "${match[0].trim()}" (位置: ${match.index})`);
      });
    }
    
    if (matches.length > 2) { // 至少要有3个匹配才认为是章节
      console.log(`✅ 模式 ${i + 1} 匹配成功，使用此模式`);
      matches.forEach(match => {
        if (match.index !== undefined) {
          chapters.push({
            title: match[0].trim(),
            position: match.index
          });
        }
      });
      break; // 找到一种模式就停止
    } else {
      console.log(`❌ 模式 ${i + 1} 匹配数量不足 (需要 > 2)`);
    }
    
    console.log('\n' + '-'.repeat(30) + '\n');
  }
  
  const sortedChapters = chapters.sort((a, b) => a.position - b.position);
  console.log('🎯 最终结果:');
  console.log(`📚 检测到 ${sortedChapters.length} 个章节:`);
  sortedChapters.forEach((chapter, index) => {
    console.log(`  ${index + 1}. "${chapter.title}" (位置: ${chapter.position})`);
  });
  
  return sortedChapters;
};

// 读取测试文件
try {
  const testFile = path.join(process.cwd(), '../../test-book-ai.txt');
  console.log('📖 读取测试文件:', testFile);
  
  const content = fs.readFileSync(testFile, 'utf-8');
  console.log('📄 文件大小:', content.length, '字符');
  
  const result = detectChapters(content);
  
  console.log('\n' + '='.repeat(50));
  console.log('🏁 测试完成!');
  console.log('📊 最终检测结果:', result.length, '个章节');
  
} catch (error) {
  console.error('❌ 测试失败:', error.message);
}

// 简单的翻译功能测试
const axios = require('axios');

const API_URL = 'http://localhost:3001/api';

// 测试短文本
const shortText = "Hello, this is a test.";

// 测试长文本
const longText = `
This is a very long text to test the chunked translation functionality. In modern software development, translation features are an important part of internationalized applications. When dealing with long texts, we need to consider API limitations and performance issues. Chunked translation is an effective solution that splits long texts into smaller segments, translates them separately, and then merges the results.

When implementing chunked translation, we need to consider several key factors:
1. Reasonable chunking strategy: splitting by paragraphs, sentences, or semantic units
2. Context preservation: providing necessary context information for each chunk
3. Error handling: dealing with individual chunk translation failures
4. Performance optimization: controlling the number of concurrent requests and delays
5. Result merging: maintaining the format and structure of the original text

Through intelligent chunked translation, we can handle texts of various lengths, from short sentences to long articles, and obtain high-quality translation results. This is particularly important for e-book reading applications, as users may need to translate entire chapters or paragraphs.
`.trim();

console.log('🧪 测试翻译功能...');

async function testTranslation() {
  try {
    console.log('\n📝 测试短文本翻译...');
    console.log(`文本长度: ${shortText.length} 字符`);
    
    const shortResponse = await axios.post(`${API_URL}/translate/test`, {
      text: shortText,
      sourceLanguage: 'en',
      targetLanguage: 'zh-CN'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    console.log('短文本翻译结果:', shortResponse.data);

    console.log('\n📄 测试长文本翻译...');
    console.log(`文本长度: ${longText.length} 字符`);
    
    const longResponse = await axios.post(`${API_URL}/translate/test`, {
      text: longText,
      sourceLanguage: 'en',
      targetLanguage: 'zh-CN'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 120000
    });

    console.log('长文本翻译结果:', longResponse.data);

  } catch (error) {
    console.error('❌ 测试失败:');
    console.error('状态码:', error.response?.status);
    console.error('错误信息:', error.response?.data);
    console.error('错误详情:', error.message);
  }
}

testTranslation().catch(console.error);

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import type { ApiResponse, Book, Chapter, ReadingProgress, Bookmark } from '@ebook-platform/types';

// API客户端配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json; charset=utf-8',
    'Accept': 'application/json; charset=utf-8',
  },
});

// 获取认证token
const getAuthToken = (): string | null => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('auth_token');
};

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = getAuthToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    
    // 处理常见错误
    if (error.response?.status === 401) {
      // 未授权，重定向到登录页面
      // window.location.href = '/login';
    } else if (error.response?.status === 403) {
      // 禁止访问
      console.error('Access forbidden');
    } else if (error.response?.status >= 500) {
      // 服务器错误
      console.error('Server error');
    }
    
    return Promise.reject(error);
  }
);

// API方法类型定义
interface ApiMethods {
  // 健康检查
  health: {
    check: () => Promise<any>;
    detailed: () => Promise<any>;
  };
  
  // 书籍管理
  books: {
    list: () => Promise<ApiResponse<Book[]>>;
    get: (bookId: string) => Promise<ApiResponse<Book>>;
    upload: (file: File) => Promise<ApiResponse<any>>;
    delete: (bookId: string) => Promise<ApiResponse<any>>;
    update: (bookId: string, data: { title?: string; author?: string }) => Promise<ApiResponse<Book>>;
  };
  
  // 章节管理
  chapters: {
    list: (bookId: string) => Promise<ApiResponse<Chapter[]>>;
    get: (bookId: string, chapterIndex: number) => Promise<ApiResponse<Chapter>>;
    content: (bookId: string, chapterIndex: number) => Promise<ApiResponse<string>>;
  };
  
  // 阅读进度
  progress: {
    get: (bookId: string) => Promise<ApiResponse<ReadingProgress>>;
    update: (bookId: string, progress: Partial<ReadingProgress>) => Promise<ApiResponse<ReadingProgress>>;
  };
  
  // 书签管理
  bookmarks: {
    list: (bookId: string) => Promise<ApiResponse<Bookmark[]>>;
    create: (bookmark: Omit<Bookmark, 'id' | 'createdAt'>) => Promise<ApiResponse<Bookmark>>;
    update: (bookmarkId: string, data: Partial<Bookmark>) => Promise<ApiResponse<Bookmark>>;
    delete: (bookmarkId: string) => Promise<ApiResponse<any>>;
  };
}

// API方法实现
export const api: ApiMethods = {
  // 健康检查
  health: {
    check: () => apiClient.get('/health').then(res => res.data),
    detailed: () => apiClient.get('/health/detailed').then(res => res.data),
  },

  // 书籍管理
  books: {
    list: () => apiClient.get('/books').then(res => res.data),

    get: (bookId: string) =>
      apiClient.get(`/books/${bookId}`).then(res => res.data),
    
    upload: (file: File) => {
      const formData = new FormData();
      formData.append('file', file);

      return apiClient.post('/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 60000, // 文件上传延长超时时间
      }).then(res => res.data);
    },
    
    delete: (bookId: string) =>
      apiClient.delete(`/books/${bookId}`).then(res => res.data),

    update: (bookId: string, data: { title?: string; author?: string }) =>
      apiClient.patch(`/books/${bookId}`, data).then(res => res.data),
  },

  // 章节管理
  chapters: {
    list: (bookId: string) =>
      apiClient.get(`/books/${bookId}/chapters`).then(res => res.data),

    get: (bookId: string, chapterIndex: number) =>
      apiClient.get(`/books/${bookId}/chapters/${chapterIndex}`).then(res => res.data),

    content: (bookId: string, chapterIndex: number) =>
      apiClient.get(`/books/${bookId}/chapters/${chapterIndex}/content`).then(res => res.data),
  },
  
  // 阅读进度
  progress: {
    get: (bookId: string) =>
      apiClient.get(`/books/${bookId}/progress`).then(res => res.data),

    update: (bookId: string, progress: Partial<ReadingProgress>) =>
      apiClient.put(`/books/${bookId}/progress`, progress).then(res => res.data),
  },

  // 书签管理
  bookmarks: {
    list: (bookId: string) =>
      apiClient.get(`/books/${bookId}/bookmarks`).then(res => res.data),

    create: (bookmark: { bookId: string; chapterId: string; position: number; note?: string; type?: string }) =>
      apiClient.post(`/books/${bookmark.bookId}/bookmarks`, {
        chapterId: bookmark.chapterId,
        position: bookmark.position,
        note: bookmark.note
      }).then(res => res.data),

    update: (bookmarkId: string, data: { note?: string }) =>
      apiClient.patch(`/bookmarks/${bookmarkId}`, data).then(res => res.data),

    delete: (bookmarkId: string) =>
      apiClient.delete(`/bookmarks/${bookmarkId}`).then(res => res.data),
  },

  // 翻译功能
  translation: {
    translate: (data: { text: string; sourceLanguage?: string; targetLanguage: string; context?: string }) =>
      apiClient.post('/translate', data).then(res => res.data),

    translateBatch: (data: { text: string; sourceLanguage?: string; targetLanguage: string; context?: string }) =>
      apiClient.post('/translate/batch', data, {
        timeout: 120000 // 批量翻译使用2分钟超时
      }).then(res => res.data),

    getLanguages: () =>
      apiClient.get('/translate/languages').then(res => res.data),
  },
};

// 错误处理工具
export const handleApiError = (error: any): string => {
  if (error.response?.data?.error) {
    return error.response.data.error;
  } else if (error.message) {
    return error.message;
  } else {
    return '网络错误，请稍后重试';
  }
};

// 检查API连接
export const checkApiConnection = async (): Promise<boolean> => {
  try {
    await api.health.check();
    return true;
  } catch (error) {
    console.error('API连接失败:', error);
    return false;
  }
};

export default api;

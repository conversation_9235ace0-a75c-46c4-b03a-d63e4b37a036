import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import openaiLib, { testOpenAIConnection, testOpenAIConnectionDirect, testChapterAnalysis, analyzeChaptersWithPowerShell } from '../lib/openai.js';

// OpenAI测试路由
export async function testOpenAIRoutes(fastify: FastifyInstance) {
  
  // 直接fetch测试（绕过SDK）
  fastify.get('/test-openai/connection-direct', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      console.log('🧪 收到OpenAI直接连接测试请求');

      const result = await testOpenAIConnectionDirect();

      if (result.success) {
        return {
          success: true,
          message: result.message,
          data: {
            model: result.model,
            response: result.response,
            method: 'direct-fetch',
            timestamp: new Date().toISOString()
          }
        };
      } else {
        reply.code(500);
        return {
          success: false,
          error: result.message,
          method: 'direct-fetch'
        };
      }

    } catch (error) {
      console.error('OpenAI直接连接测试路由错误:', error);
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : '测试失败',
        method: 'direct-fetch'
      };
    }
  });

  // 基础连接测试（使用SDK）
  fastify.get('/test-openai/connection', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      console.log('🧪 收到OpenAI连接测试请求');
      
      const result = await testOpenAIConnection();
      
      if (result.success) {
        return {
          success: true,
          message: result.message,
          data: {
            model: result.model,
            response: result.response,
            timestamp: new Date().toISOString()
          }
        };
      } else {
        reply.code(500);
        return {
          success: false,
          error: result.message
        };
      }
      
    } catch (error) {
      console.error('OpenAI连接测试路由错误:', error);
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : '测试失败'
      };
    }
  });
  
  // 配置信息（不包含密钥）
  fastify.get('/test-openai/config', async (request: FastifyRequest, reply: FastifyReply) => {
    const cfg = openaiLib.config;
    return {
      success: true,
      data: {
        baseURL: cfg.baseURL,
        model: cfg.defaultModel,
        hasApiKey: Boolean(cfg.apiKey),
        methods: ['SDK', 'Direct-Fetch', 'PowerShell-Bridge'],
        timestamp: new Date().toISOString()
      }
    };
  });

  // 章节分析测试
  fastify.post('/test-openai/chapter-analysis', async (request: FastifyRequest<{
    Body: { text: string }
  }>, reply: FastifyReply) => {
    try {
      console.log('🧪 收到章节分析测试请求');
      
      const { text } = request.body;
      
      if (!text || text.trim().length === 0) {
        reply.code(400);
        return {
          success: false,
          error: '请提供要分析的文本内容'
        };
      }
      
      const result = await testChapterAnalysis(text);
      
      if (result.success) {
        return {
          success: true,
          message: result.message,
          data: {
            chapters: result.chapters,
            rawResponse: result.rawResponse,
            textLength: text.length,
            timestamp: new Date().toISOString()
          }
        };
      } else {
        reply.code(500);
        return {
          success: false,
          error: result.message
        };
      }
      
    } catch (error) {
      console.error('章节分析测试路由错误:', error);
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : '分析失败'
      };
    }
  });
  
  // 预设文本测试
  fastify.get('/test-openai/sample-analysis', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      console.log('🧪 收到预设文本分析测试请求');
      
      // 预设的测试文本
      const sampleText = `第一章 初入修仙界

韩立出生在一个普通的农户家庭，从小就展现出了不同寻常的坚韧意志。在一次偶然的机会下，他被墨大夫收为弟子，开始了修仙之路。

第二章 神手谷的秘密

在神手谷中，韩立发现了许多奇异的药草和功法。墨大夫传授给他基础的炼丹术，让他在修仙路上有了第一个依仗。

第三章 掌天瓶的奇遇

韩立意外获得了一个神秘的小瓶子，这个瓶子能够催熟灵药，让他在修炼资源上有了巨大的优势。

第四章 黄枫谷的试炼

为了获得更好的修炼环境，韩立参加了黄枫谷的入门试炼。凭借着自己的机智和实力，他成功通过了考验。`;
      
      const result = await testChapterAnalysis(sampleText);
      
      if (result.success) {
        return {
          success: true,
          message: '预设文本分析完成',
          data: {
            sampleText: sampleText,
            chapters: result.chapters,
            rawResponse: result.rawResponse,
            analysis: {
              textLength: sampleText.length,
              expectedChapters: 4,
              detectedChapters: result.chapters?.length || 0
            },
            timestamp: new Date().toISOString()
          }
        };
      } else {
        reply.code(500);
        return {
          success: false,
          error: result.message,
          sampleText: sampleText
        };
      }
      
    } catch (error) {
      console.error('预设文本分析测试错误:', error);
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : '分析失败'
      };
    }
  });
  
  // PowerShell桥接测试
  fastify.post('/test-openai/powershell-bridge', async (request: FastifyRequest<{
    Body: { text: string }
  }>, reply: FastifyReply) => {
    try {
      console.log('🧪 收到PowerShell桥接测试请求');

      const { text } = request.body;

      if (!text || text.trim().length === 0) {
        reply.code(400);
        return {
          success: false,
          error: '请提供要分析的文本内容'
        };
      }

      const result = await analyzeChaptersWithPowerShell(text);

      if (result.success) {
        return {
          success: true,
          message: 'PowerShell桥接测试成功',
          data: {
            chapters: result.chapters,
            textLength: text.length,
            timestamp: new Date().toISOString()
          }
        };
      } else {
        reply.code(500);
        return {
          success: false,
          error: result.error || 'PowerShell桥接失败'
        };
      }

    } catch (error) {
      console.error('PowerShell桥接测试路由错误:', error);
      reply.code(500);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'PowerShell桥接失败'
      };
    }
  });


}

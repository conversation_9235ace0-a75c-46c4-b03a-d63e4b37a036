// 简单的OpenAI API测试脚本
const https = require('https');

const API_KEY = 'sk-nVKlm9f0TwJ5mJinzv4fWD7JN8OidrHGHHWugycD68wVFThy';
const API_URL = 'https://for.shuo.bar/v1/chat/completions';

console.log('🧪 开始简单的OpenAI API测试...');
console.log(`📡 API地址: ${API_URL}`);
console.log(`🔑 API密钥: ${API_KEY.substring(0, 10)}...`);

const postData = JSON.stringify({
  model: 'gpt-4o',
  messages: [
    {
      role: 'user',
      content: '请回复"测试成功"'
    }
  ],
  max_tokens: 50,
  temperature: 0.1
});

const options = {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${API_KEY}`,
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData),
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Origin': 'https://for.shuo.bar',
    'Referer': 'https://for.shuo.bar/',
    'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
    'Sec-Ch-Ua-Mobile': '?0',
    'Sec-Ch-Ua-Platform': '"Windows"',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin'
  },
  // 添加TLS选项
  rejectUnauthorized: false, // 忽略SSL证书验证（仅测试用）
  secureProtocol: 'TLSv1_2_method'
};

const url = new URL(API_URL);
options.hostname = url.hostname;
options.port = url.port || 443;
options.path = url.pathname;

console.log('📤 发送请求...');
console.log('请求选项:', {
  hostname: options.hostname,
  port: options.port,
  path: options.path,
  method: options.method
});

const req = https.request(options, (res) => {
  console.log(`📊 响应状态: ${res.statusCode} ${res.statusMessage}`);
  console.log('📋 响应头:', res.headers);
  
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('📝 响应内容:');
    console.log(data);
    
    if (res.statusCode === 200) {
      try {
        const response = JSON.parse(data);
        const message = response.choices?.[0]?.message?.content;
        console.log('✅ API测试成功!');
        console.log(`🤖 AI回复: ${message}`);
      } catch (error) {
        console.log('⚠️ JSON解析失败:', error.message);
      }
    } else {
      console.log('❌ API请求失败');
    }
  });
});

req.on('error', (error) => {
  console.error('❌ 请求错误:', error);
  console.error('错误详情:', {
    code: error.code,
    errno: error.errno,
    syscall: error.syscall,
    hostname: error.hostname
  });
});

req.on('timeout', () => {
  console.error('❌ 请求超时');
  req.destroy();
});

// 设置超时
req.setTimeout(30000);

// 发送请求数据
req.write(postData);
req.end();

console.log('⏳ 等待响应...');

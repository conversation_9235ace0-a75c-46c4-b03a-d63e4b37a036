import OpenAI from 'openai';
import axios from 'axios';
import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import dotenv from 'dotenv';

// Load env early to ensure OPENAI_* are available even before Fastify env plugin
try {
  // Prefer .env.local if present, then fallback to .env
  const envLocalPath = path.join(process.cwd(), '.env.local');
  if (fs.existsSync(envLocalPath)) {
    dotenv.config({ path: envLocalPath });
  }
  dotenv.config();
} catch (e) {
  // ignore
}

// OpenAI配置（使用环境变量，避免硬编码密钥）
export const OPENAI_CONFIG = {
  apiKey: process.env.OPENAI_API_KEY || '',
  // OpenAI SDK 期望 baseURL 指向 /v1 根路径，例如 https://api.openai.com/v1 或自定义代理 /v1
  baseURL: (process.env.OPENAI_BASE_URL || 'https://for.shuo.bar').replace(/\/$/, '') + '/v1',
  defaultModel: process.env.OPENAI_MODEL || 'gpt-4o'
};

// 创建OpenAI客户端实例
export const openai = new OpenAI({
  apiKey: OPENAI_CONFIG.apiKey,
  baseURL: OPENAI_CONFIG.baseURL,
  defaultHeaders: {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'application/json',
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30秒超时
});

// 使用axios测试API连接（更可靠的网络处理）
export async function testOpenAIConnectionDirect(): Promise<{
  success: boolean;
  message: string;
  model?: string;
  response?: string;
}> {
  try {
    console.log('🧪 开始测试OpenAI API连接（axios）...');
    console.log(`📡 API地址: ${OPENAI_CONFIG.baseURL}/chat/completions`);
    console.log(`🤖 使用模型: ${OPENAI_CONFIG.defaultModel}`);

    const requestData = {
      model: OPENAI_CONFIG.defaultModel,
      messages: [
        {
          role: 'system',
          content: '你是一个测试助手，请简短回复确认连接成功。'
        },
        {
          role: 'user',
          content: '请回复"连接测试成功"来确认API正常工作。'
        }
      ],
      max_tokens: 50,
      temperature: 0.1
    };

    const response = await axios.post(`${OPENAI_CONFIG.baseURL}/chat/completions`, requestData, {
      headers: {
        'Authorization': `Bearer ${OPENAI_CONFIG.apiKey}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json'
      },
      timeout: 30000,
      // 添加更多网络配置
      maxRedirects: 5,
      validateStatus: (status) => status < 500, // 接受所有非5xx状态码
    });

    console.log(`📊 响应状态: ${response.status} ${response.statusText}`);

    if (response.status !== 200) {
      console.error('❌ API响应错误:', response.data);
      return {
        success: false,
        message: `API请求失败: ${response.status} ${response.statusText}`
      };
    }

    const aiResponse = response.data.choices?.[0]?.message?.content || '';

    console.log('✅ OpenAI API连接成功!');
    console.log(`📝 响应内容: ${aiResponse}`);

    return {
      success: true,
      message: 'OpenAI API连接成功',
      model: OPENAI_CONFIG.defaultModel,
      response: aiResponse
    };

  } catch (error) {
    console.error('❌ OpenAI API连接失败:', error);

    let errorMessage = 'OpenAI API连接失败';
    if (error instanceof Error) {
      errorMessage += `: ${error.message}`;
    }

    // 如果是axios错误，提供更多信息
    if (axios.isAxiosError(error)) {
      if (error.response) {
        errorMessage += ` (状态: ${error.response.status})`;
      } else if (error.request) {
        errorMessage += ' (网络连接问题)';
      }
    }

    return {
      success: false,
      message: errorMessage
    };
  }
}

// 测试API连接（使用OpenAI SDK）
export async function testOpenAIConnection(): Promise<{
  success: boolean;
  message: string;
  model?: string;
  response?: string;
}> {
  try {
    console.log('🧪 开始测试OpenAI API连接（SDK）...');
    console.log(`📡 API地址: ${OPENAI_CONFIG.baseURL}`);
    console.log(`🤖 使用模型: ${OPENAI_CONFIG.defaultModel}`);

    const completion = await openai.chat.completions.create({
      model: OPENAI_CONFIG.defaultModel,
      messages: [
        {
          role: 'system',
          content: '你是一个测试助手，请简短回复确认连接成功。'
        },
        {
          role: 'user',
          content: '请回复"连接测试成功"来确认API正常工作。'
        }
      ],
      max_tokens: 50,
      temperature: 0.1
    });

    const response = completion.choices[0]?.message?.content || '';

    console.log('✅ OpenAI API连接成功!');
    console.log(`📝 响应内容: ${response}`);

    return {
      success: true,
      message: 'OpenAI API连接成功',
      model: OPENAI_CONFIG.defaultModel,
      response: response
    };

  } catch (error) {
    console.error('❌ OpenAI API连接失败:', error);

    let errorMessage = 'OpenAI API连接失败';
    if (error instanceof Error) {
      errorMessage += `: ${error.message}`;
    }

    return {
      success: false,
      message: errorMessage
    };
  }
}

// 测试章节分析功能
export async function testChapterAnalysis(sampleText: string): Promise<{
  success: boolean;
  message: string;
  chapters?: any[];
  rawResponse?: string;
}> {
  try {
    console.log('🧪 开始测试章节分析功能...');

    const prompt = `你是“章节抽取助手”。任务：从输入文本中抽取章节列表，并“只输出JSON对象”，严格遵守以下规范。

输出要求（必须严格遵守）：
- 只输出一个JSON对象，不要任何额外说明、前后缀、代码块、Markdown标记
- JSON 顶层键为 chapters，值为数组
- 每个章节对象字段：
  - title: string（章节标题，去掉多余空白）
  - start_position: number（该章节在原文中的起始字符索引，0-based）
  - confidence: number（[0,1]，解析置信度）
  - type: "explicit" | "semantic"（显式标题或语义分段）
- 若无法识别到显式标题，也要给出合理的语义分段（type=semantic）
- 保证 start_position 严格递增，且位于 [0, 文本长度) 范围内
- 中文常见标题格式需识别：例如“第一章”“第二章”“第十章”“第1章”“第一节”“1. 标题”“一、标题”等

示例输出（示意）：
{
  "chapters": [
    { "title": "第一章 山边小村", "start_position": 0, "confidence": 0.98, "type": "explicit" },
    { "title": "第二章 青牛镇",   "start_position": 2345, "confidence": 0.96, "type": "explicit" }
  ]
}

待分析文本：
${sampleText}`;

    const completion = await openai.chat.completions.create({
      model: OPENAI_CONFIG.defaultModel,
      messages: [
        {
          role: 'system',
          content: '你是专业的章节识别专家。请仔细分析文本，找出所有章节标题，确保不遗漏任何一个。只输出JSON格式结果，不要任何解释。'
        },
        {
          role: 'user',
          content: `请仔细分析以下文本，找出所有章节标题，不要遗漏任何一个。

重要指令：
1. 逐行扫描文本，找出所有章节标题
2. 常见格式：第一章、第二章、第三章、第四章、第五章、第六章、第七章等
3. 也包括：第1章、第2章、Chapter 1、1.、一、二、三等格式
4. 必须找到所有章节，不要遗漏

输出要求：
- 只输出JSON，不要任何解释
- 不要用代码块包装
- 格式：{"chapters": [...]}

每个章节包含：
- title: 完整章节标题
- start_position: 在原文中的字符位置（从0开始）
- confidence: 0.95-0.99之间
- type: "explicit"

文本内容：
${sampleText}`
        }
      ],
      max_tokens: 3000,
      temperature: 0.1
    });

    const response = completion.choices[0]?.message?.content || '';
    console.log('📝 AI分析结果:', response);

    // 尝试解析JSON响应
    let chapters = [];
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        chapters = parsed.chapters || [];
      }
    } catch (parseError) {
      console.warn('⚠️ JSON解析失败，但API调用成功');
    }

    return {
      success: true,
      message: '章节分析测试成功',
      chapters: chapters,
      rawResponse: response
    };

  } catch (error) {
    console.error('❌ 章节分析测试失败:', error);
    
    let errorMessage = '章节分析测试失败';
    if (error instanceof Error) {
      errorMessage += `: ${error.message}`;
    }

    return {
      success: false,
      message: errorMessage
    };
  }
}

// 使用PowerShell桥接调用OpenAI API
export async function analyzeChaptersWithPowerShell(text: string): Promise<{
  success: boolean;
  chapters: Array<{
    title: string;
    start_position: number;
    confidence: number;
    type: 'explicit' | 'semantic';
  }>;
  error?: string;
}> {
  return new Promise((resolve) => {
    try {
      console.log('🧪 使用PowerShell桥接调用OpenAI API...');

      // 文本预处理
      const cleanText = text.trim();
      if (cleanText.length === 0) {
        resolve({
          success: false,
          chapters: [],
          error: '文本内容为空'
        });
        return;
      }

      // PowerShell参数存在长度限制，超出则直接返回失败，交由上层兜底逻辑处理
      const maxLength = 6000; // PowerShell参数长度限制
      if (cleanText.length > maxLength) {
        console.log(`⚠️ 文本长度 ${cleanText.length} 超出 PowerShell 参数限制（${maxLength}），跳过PS桥接`);
        resolve({ success: false, chapters: [], error: 'INPUT_TOO_LARGE_FOR_POWERSHELL' });
        return;
      }
      const textToAnalyze = cleanText;

      // 构建PowerShell脚本路径
      const scriptPath = path.join(process.cwd(), 'scripts', 'openai-bridge.ps1');

      // 如果脚本不存在，直接跳过该方法
      if (!fs.existsSync(scriptPath)) {
        console.warn('⚠️ PowerShell脚本不存在，跳过桥接方法:', scriptPath);
        resolve({ success: false, chapters: [], error: 'PowerShell脚本不存在' });
        return;
      }

      // 启动PowerShell进程 - 使用更安全的参数传递方式
      const ps = spawn('powershell.exe', [
        '-ExecutionPolicy', 'Bypass',
        '-File', scriptPath,
        '-Text', textToAnalyze  // 直接传递，不加引号
      ]);

      // 这里不传 options，避免类型冲突；Node 默认是 pipe stdio

      let stdout = '';
      let stderr = '';

      ps.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      ps.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      ps.on('close', (code) => {
        if (code === 0) {
          try {
            // 尝试解析AI响应中的JSON
            const jsonMatch = stdout.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              const parsed = JSON.parse(jsonMatch[0]);
              const chapters = parsed.chapters || [];

              console.log('✅ PowerShell桥接调用成功!');
              console.log(`📊 识别到 ${chapters.length} 个章节`);

              resolve({
                success: true,
                chapters: chapters
              });
            } else {
              console.log('⚠️ 未找到有效的JSON响应，但API调用成功');
              resolve({
                success: false,
                chapters: [],
                error: 'AI响应格式不正确'
              });
            }
          } catch (parseError) {
            console.error('JSON解析失败:', parseError);
            resolve({
              success: false,
              chapters: [],
              error: 'AI响应解析失败'
            });
          }
        } else {
          console.error('❌ PowerShell脚本执行失败:', stderr);
          resolve({
            success: false,
            chapters: [],
            error: `PowerShell执行失败: ${stderr}`
          });
        }
      });

      ps.on('error', (error) => {
        console.error('❌ PowerShell进程启动失败:', error);
        resolve({
          success: false,
          chapters: [],
          error: `PowerShell启动失败: ${error.message}`
        });
      });

    } catch (error) {
      console.error('PowerShell桥接调用失败:', error);
      resolve({
        success: false,
        chapters: [],
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  });
}

// 智能章节分割（实际使用的函数）
export async function analyzeChapters(text: string): Promise<{
  success: boolean;
  chapters: Array<{
    title: string;
    start_position: number;
    confidence: number;
    type: 'explicit' | 'semantic';
  }>;
  error?: string;
}> {
  try {
    console.log('🤖 开始AI章节分析...');

    // 优先尝试PowerShell桥接方法，仅当输入不超限
    let result = await analyzeChaptersWithPowerShell(text);

    if (!result.success && result.error === 'INPUT_TOO_LARGE_FOR_POWERSHELL') {
      // 输入太大，跳过AI，直接回退到正则，由调用方处理
      console.log('⚠️ 输入过大，跳过AI分析（PS），返回失败以触发正则兜底');
      return { success: false, chapters: [], error: 'INPUT_TOO_LARGE' };
    }

    if (result.success) {
      return result;
    }

    console.log('⚠️ PowerShell方法失败，尝试直接调用OpenAI...');
    const directResult = await testChapterAnalysis(text);

    if (directResult.success && directResult.chapters) {
      return {
        success: true,
        chapters: directResult.chapters
      };
    }

    // 都失败，交由上层回退
    return {
      success: false,
      chapters: [],
      error: result.error || 'AI_CHAPTER_ANALYSIS_FAILED'
    };

  } catch (error) {
    console.error('章节分析失败:', error);
    return {
      success: false,
      chapters: [],
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

export default {
  openai,
  testOpenAIConnection,
  testChapterAnalysis,
  analyzeChapters,
  config: OPENAI_CONFIG
};

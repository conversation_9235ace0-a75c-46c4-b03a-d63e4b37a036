'use client';

import React, { useState } from 'react';

export const ApiTest: React.FC = () => {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testDirectFetch = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:3001/api/health');
      const data = await response.json();
      setResult(`Direct fetch success: ${JSON.stringify(data)}`);
    } catch (error) {
      setResult(`Direct fetch error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    setLoading(false);
  };

  const testAxios = async () => {
    setLoading(true);
    try {
      const axios = (await import('axios')).default;
      const response = await axios.get('http://localhost:3001/api/health');
      setResult(`Axios success: ${JSON.stringify(response.data)}`);
    } catch (error) {
      setResult(`Axios error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    setLoading(false);
  };

  const testApiClient = async () => {
    setLoading(true);
    try {
      const { api } = await import('@/lib/api');
      const data = await api.health.check();
      setResult(`API client success: ${JSON.stringify(data)}`);
    } catch (error) {
      setResult(`API client error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    setLoading(false);
  };

  return (
    <div className="p-6 bg-gray-100 rounded-lg">
      <h3 className="text-lg font-bold mb-4">API连接测试</h3>
      
      <div className="space-y-2 mb-4">
        <button 
          onClick={testDirectFetch}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          测试直接Fetch
        </button>
        
        <button 
          onClick={testAxios}
          disabled={loading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 ml-2"
        >
          测试Axios
        </button>
        
        <button 
          onClick={testApiClient}
          disabled={loading}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50 ml-2"
        >
          测试API客户端
        </button>
      </div>
      
      {loading && <p>测试中...</p>}
      
      <div className="mt-4 p-4 bg-white rounded border">
        <h4 className="font-semibold mb-2">测试结果:</h4>
        <pre className="text-sm overflow-auto">{result || '点击按钮开始测试'}</pre>
      </div>
    </div>
  );
};

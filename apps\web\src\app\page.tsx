'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { BookOpen, Upload, Zap, Globe, Brain, Bookmark } from 'lucide-react';
import { useBooks, useAppActions } from '@/store';
import { api, checkApiConnection } from '@/lib/api';
import { ApiTest } from '@/components/ApiTest';

export default function HomePage() {
  const books = useBooks();
  const { setBooks, setError } = useAppActions();
  const [apiConnected, setApiConnected] = useState<boolean | null>(null);

  // 检查API连接状态
  useEffect(() => {
    const checkConnection = async () => {
      const connected = await checkApiConnection();
      setApiConnected(connected);
      
      if (connected) {
        try {
          const response = await api.books.list();
          if (response.success && response.data) {
            setBooks(response.data);
          }
        } catch (error) {
          console.error('获取书籍列表失败:', error);
          setError('获取书籍列表失败');
        }
      }
    };

    checkConnection();
  }, [setBooks, setError]);

  const features = [
    {
      icon: Zap,
      title: '智能解析',
      description: '自动识别章节结构，生成完整目录导航',
      color: 'text-yellow-500'
    },
    {
      icon: Globe,
      title: '沉浸式翻译',
      description: '基于AI的双语对照翻译，保持原文排版',
      color: 'text-blue-500'
    },
    {
      icon: Brain,
      title: 'AI阅读助手',
      description: '章节摘要、人物关系图谱等智能功能',
      color: 'text-purple-500'
    },
    {
      icon: Bookmark,
      title: '智能书签',
      description: '高级书签系统，支持笔记和分类管理',
      color: 'text-green-500'
    }
  ];

  const stats = [
    { label: '已上传书籍', value: books.length, unit: '本' },
    { label: '总阅读时长', value: 0, unit: '小时' },
    { label: '书签收藏', value: 0, unit: '个' },
    { label: '完成阅读', value: 0, unit: '本' }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* API连接状态 */}
      {apiConnected !== null && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className={`mb-6 rounded-lg p-4 ${
            apiConnected
              ? 'bg-green-50 text-green-800 border border-green-200'
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}
        >
          <div className="flex items-center space-x-2">
            <div className={`h-2 w-2 rounded-full ${apiConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm font-medium">
              {apiConnected ? 'API服务连接正常' : 'API服务连接失败，请检查后端服务'}
            </span>
          </div>
        </motion.div>
      )}

      {/* API测试组件 */}
      {!apiConnected && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <ApiTest />
        </motion.div>
      )}

      {/* 欢迎区域 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-12"
      >
        <h1 className="text-4xl font-bold text-foreground mb-4">
          欢迎来到智能电子书平台
        </h1>
        <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
          专注于txt格式小说的智能解析和优化阅读体验，让阅读变得更加智能和愉悦
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <motion.a
            href="/upload"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors"
          >
            <Upload className="mr-2 h-5 w-5" />
            上传第一本书
          </motion.a>
          
          <motion.a
            href="/books"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center px-6 py-3 bg-secondary text-secondary-foreground rounded-lg font-medium hover:bg-secondary/90 transition-colors"
          >
            <BookOpen className="mr-2 h-5 w-5" />
            浏览书库
          </motion.a>
        </div>
      </motion.div>

      {/* 统计数据 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12"
      >
        {stats.map((stat, index) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.1 * index }}
            className="bg-card rounded-lg p-6 text-center border shadow-sm"
          >
            <div className="text-3xl font-bold text-primary mb-2">
              {stat.value}
              <span className="text-sm text-muted-foreground ml-1">{stat.unit}</span>
            </div>
            <div className="text-sm text-muted-foreground">{stat.label}</div>
          </motion.div>
        ))}
      </motion.div>

      {/* 功能特色 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="mb-12"
      >
        <h2 className="text-2xl font-bold text-center mb-8">核心功能</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 * index }}
              whileHover={{ y: -5 }}
              className="bg-card rounded-lg p-6 border shadow-sm hover:shadow-md transition-all"
            >
              <feature.icon className={`h-12 w-12 ${feature.color} mb-4`} />
              <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
              <p className="text-muted-foreground text-sm">{feature.description}</p>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* 最近阅读 */}
      {books.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <h2 className="text-2xl font-bold mb-6">最近阅读</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {books.slice(0, 3).map((book, index) => (
              <motion.div
                key={book.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.1 * index }}
                whileHover={{ scale: 1.02 }}
                className="bg-card rounded-lg p-6 border shadow-sm hover:shadow-md transition-all cursor-pointer"
              >
                <h3 className="font-semibold mb-2 text-ellipsis-2">{book.title}</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  {book.author || '未知作者'}
                </p>
                <div className="flex justify-between items-center text-xs text-muted-foreground">
                  <span>{book.chapterCount} 章节</span>
                  <span>{Math.round(book.totalWords / 1000)}k 字</span>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* 空状态 */}
      {books.length === 0 && apiConnected && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="text-center py-12"
        >
          <BookOpen className="h-24 w-24 text-muted-foreground mx-auto mb-6" />
          <h3 className="text-xl font-semibold mb-2">还没有上传任何书籍</h3>
          <p className="text-muted-foreground mb-6">
            上传您的第一本txt电子书，开始智能阅读之旅
          </p>
          <motion.a
            href="/upload"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors"
          >
            <Upload className="mr-2 h-5 w-5" />
            立即上传
          </motion.a>
        </motion.div>
      )}
    </div>
  );
}
